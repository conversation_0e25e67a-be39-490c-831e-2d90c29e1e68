import { RouteKey, RoutePathMap } from '../config';
import { TSTRouteObject } from '../tst-router-dom/tst-route';
import { commonCheckPermission } from '../utils/permission';
import IconFont from '@/components/IconFont';
import { Auth } from '@/constants/auth';

export const taskCenterRoute: TSTRouteObject = {
  key: RouteKey.TaskCenter,
  path: RoutePathMap[RouteKey.TaskCenter],
  menu: { name: '任务中心', icon: <IconFont type="icon-renwu" /> },
  handle: {
    breadcrumb: '任务中心',
  },
  checkPermission: (data) => {
    return commonCheckPermission(data, [Auth['任务中心']]);
  },
  children: [
    {
      key: RouteKey.TCList,
      path: RoutePathMap[RouteKey.TCList],

      menu: { name: '任务中心', icon: <IconFont type="icon-renwu" /> },
      // handle: {
      //   breadcrumb: '任务中心',
      // },
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth.策略_任务中心]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/taskCenter/list/index');

        return { Component };
      },
      children: [
        {
          key: RouteKey.TCLDetail,
          path: RoutePathMap[RouteKey.TCLDetail],
          handle: {
            breadcrumb: '任务明细',
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/taskCenter/list/detail/index');

            return { Component };
          },
          children: [
            {
              key: RouteKey.TCLDInfo,
              path: RoutePathMap[RouteKey.TCLDInfo],
              handle: {
                breadcrumb: '任务详情',
              },
              lazy: async () => {
                const { default: Component } = await import('@/pages/taskCenter/list/detail/info/index');

                return { Component };
              },
            },
          ],
        },
        {
          key: RouteKey.TCIssueFailTaskDetail,
          path: RoutePathMap[RouteKey.TCIssueFailTaskDetail],
          handle: {
            breadcrumb: '下发失败任务明细',
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/taskCenter/list/issueFailTaskDetail');

            return { Component };
          },
        },
      ],
    },

    {
      key: RouteKey.TCTransfer,
      path: RoutePathMap[RouteKey.TCTransfer],
      handle: {
        breadcrumb: '任务转办申请',
      },
      menu: { name: '任务转办申请' },
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth.策略_任务转办申请]);
      },
      // lazy: async () => {
      //   const { default: Component } = await import('@/pages/taskCenter/transfer/index');

      //   return { Component };
      // },
      children: [
        {
          key: RouteKey.TCTransferNormal,
          path: RoutePathMap[RouteKey.TCTransferNormal],
          menu: { name: '非食安稽核任务转办申请' },
          handle: {
            breadcrumb: '非食安稽核任务转办申请',
          },
          checkPermission: (data) => {
            return commonCheckPermission(data, [Auth['非食安稽核任务转办申请']]);
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/taskCenter/transfer/notFoodsafe/index');

            return { Component };
          },
        },
        {
          key: RouteKey.TCTransferFoodsafe,
          path: RoutePathMap[RouteKey.TCTransferFoodsafe],
          menu: { name: '食安稽核任务转办申请' },
          handle: {
            breadcrumb: '食安稽核任务转办申请',
          },
          checkPermission: (data) => {
            return commonCheckPermission(data, [Auth['食安稽核任务转办申请']]);
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/taskCenter/transfer/foodsafe/index');

            return { Component };
          },
        },
      ],
    },
    {
      key: RouteKey.DifferenceAudit,
      path: RoutePathMap[RouteKey.DifferenceAudit],
      handle: {
        breadcrumb: '差异项到店审核',
      },
      menu: { name: '差异项到店审核' },
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth.差异项到店审核]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/taskCenter/differenceAudit/index');

        return { Component };
      },
    },
    {
      key: RouteKey.SelfTask,
      path: RoutePathMap[RouteKey.SelfTask],
      handle: {
        breadcrumb: '我的任务',
      },
      menu: {
        name: '我的任务',
      },
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['任务中心_我的任务']]);
      },
      children: [
        {
          key: RouteKey.TCCloud,
          path: RoutePathMap[RouteKey.TCCloud],
          menu: { name: '视频云巡检' },
          handle: {
            breadcrumb: '视频云巡检',
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/taskCenter/cloud/index');

            return { Component };
          },
          checkPermission: (data) => {
            return commonCheckPermission(data, [Auth.任务中心_我的任务_视频云巡检]);
          },
          children: [
            {
              key: RouteKey.TCCPatrol,
              path: RoutePathMap[RouteKey.TCCPatrol],
              handle: {
                breadcrumb: '巡检',
              },
              lazy: async () => {
                const { default: Component } = await import('@/pages/taskCenter/cloud/patrol/index');

                return { Component };
              },
            },
          ],
        },
        {
          key: RouteKey.ReviewTask,
          path: RoutePathMap[RouteKey.ReviewTask],
          handle: {
            breadcrumb: '点评任务',
          },
          menu: {
            name: '点评任务',
          },
          checkPermission: (data) => {
            return commonCheckPermission(data, [Auth['任务中心_我的任务_点评任务']]);
          },
          children: [
            {
              key: RouteKey.ReviewTaskComment,
              path: RoutePathMap[RouteKey.ReviewTaskComment],
              handle: {
                breadcrumb: '点评任务（一点、二点）',
              },
              menu: {
                name: '点评任务（一点、二点）',
              },
              checkPermission: (data) => {
                return commonCheckPermission(data, [Auth['任务中心_我的任务_点评任务_点评任务（一点、二点）']]);
              },
              lazy: async () => {
                const { default: Component } = await import('@/pages/selfTask/reviewTask');

                return {
                  Component,
                };
              },
            },
            {
              key: RouteKey.ReviewTaskFoodSafe,
              path: RoutePathMap[RouteKey.ReviewTaskFoodSafe],
              handle: {
                breadcrumb: '食安稽核到店辅导点评任务',
              },
              menu: {
                name: '食安稽核到店辅导点评任务',
              },
              checkPermission: (data) => {
                return commonCheckPermission(data, [Auth['任务中心_我的任务_点评任务_食安稽核到店辅导点评任务']]);
              },
              lazy: async () => {
                const { default: Component } = await import('@/pages/selfTask/reviewTask/foodSafe');

                return {
                  Component,
                };
              },
            },
            {
              key: RouteKey.CounselingReview,
              path: RoutePathMap[RouteKey.CounselingReview],
              handle: {
                breadcrumb: '食安稽核到店辅导点评',
              },
              lazy: async () => {
                const { default: Component } = await import(
                  '@/pages/selfTask/reviewTask/components/Counseling/CounselingReview'
                );

                return { Component };
              },
            },
          ],
        },
        {
          key: RouteKey.TCAppealAwaitDealTask,
          path: RoutePathMap[RouteKey.TCAppealAwaitDealTask],
          menu: { name: '非食安申诉待处理任务' },
          handle: {
            breadcrumb: '非食安申诉待处理任务',
          },
          checkPermission: (data) => {
            return commonCheckPermission(data, [Auth['任务中心_我的任务_申诉待处理任务']]);
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/taskCenter/appeal/awaitDealTask');

            return { Component };
          },
        },
        {
          key: RouteKey.TCAppealDealTask,
          path: RoutePathMap[RouteKey.TCAppealDealTask],
          menu: { name: '非食安申诉处理任务' },
          handle: {
            breadcrumb: '非食安申诉处理任务',
          },
          checkPermission: (data) => {
            return commonCheckPermission(data, [Auth['任务中心_我的任务_申诉处理任务']]);
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/taskCenter/appeal/dealTask');

            return { Component };
          },
        },
        {
          key: RouteKey.TCAppealFoodAppeal,
          path: RoutePathMap[RouteKey.TCAppealFoodAppeal],
          menu: { name: '食安申诉任务' },
          handle: {
            breadcrumb: '食安申诉任务',
          },
          checkPermission: (data) => {
            return commonCheckPermission(data, [Auth['任务中心_我的任务_食安申诉任务']]);
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/taskCenter/foodAppeal');

            return { Component };
          },
        },
      ],
    },
    {
      key: RouteKey.AuditSchedulingManage,
      path: RoutePathMap[RouteKey.AuditSchedulingManage],
      handle: {
        breadcrumb: '稽核排班管理',
      },
      menu: {
        name: '稽核排班管理',
      },
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['任务中心_稽核排班管理']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/taskCenter/auditSchedulingManage/index');

        return { Component };
      },
    },
  ],
};
