import { FC, ReactNode, useEffect, useRef } from 'react';
import {
  FormListActionType,
  ProForm,
  ProFormDatePicker,
  ProFormDateRangePicker,
  ProFormDependency,
  ProFormInstance,
  ProFormList,
  ProFormSelect,
  ProFormText,
  ProFormTimePicker,
} from '@ant-design/pro-components';
import { Button } from 'antd';
import dayjs from 'dayjs';
import {
  EdgeTriggerType,
  EdgeTriggerTypeCN,
  engineeringProgressOptions,
  partyOptions,
  TaskExecuteWayEnum,
  TaskExecuteWayTypeCN,
} from './constants';
import { StrategyCycleTypeCN } from '@/constants/strategy';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import eventEmitter from '@/utils/eventEmitter';

const executeDateOptions = Array.from({ length: 91 }, (_, index) => ({
  label: `${index}天`,
  value: index,
}));

function CustomizeExecuteTImeConfig({
  generateDateName,
  generateTimeName,
  executeDateName,
  executeTimeName,
  cycleName,
  rangeName,
  isRequired,
  isCirculate,
}: Pick<EdgeTriggerProps, 'generateDateName' | 'generateTimeName' | 'executeDateName' | 'executeTimeName'> &
  Partial<Pick<EdgeTriggerProps, 'cycleName' | 'rangeName'>> & {
    /**
     * 是否必填
     */
    isRequired?: boolean;
    /**
     * 是否为循环
     */
    isCirculate?: boolean;
  }) {
  return (
    <>
      {isCirculate ? (
        <ProFormDependency name={[cycleName]}>
          {() => {
            return (
              <ProFormDateRangePicker
                label="生成日期选择"
                name={rangeName}
                fieldProps={{
                  format: 'YYYY-MM-DD',
                }}
                {...(isRequired && { required: true, message: '请选择生成日期' })}
              />
            );
          }}
        </ProFormDependency>
      ) : (
        <ProFormDatePicker
          label="生成日期选择"
          name={generateDateName}
          {...(isRequired && { required: true, message: '请选择生成日期' })}
        />
      )}
      <ProFormTimePicker
        label="生成时间选择"
        name={generateTimeName}
        fieldProps={{
          format: 'HH:mm:ss',
        }}
        {...(isRequired && { required: true, message: '请选择生成时间' })}
      />
      {/* <ProFormDatePicker
        label="执行日期选择"
        name={executeDateName}
        {...(isRequired && { required: true, message: '请选择执行日期' })}
      /> */}
      <ProFormSelect
        label="执行日期选择"
        name={executeDateName}
        width={140}
        showSearch
        fieldProps={{
          allowClear: true,
          placeholder: '请选择执行日期',
          options: executeDateOptions,
        }}
        {...(isRequired && { required: true, message: '请选择执行日期' })}
      />
      <ProFormDependency name={[generateTimeName, executeDateName]}>
        {(target) => {
          return (
            <ProFormTimePicker
              label="执行时间选择"
              name={executeTimeName}
              fieldProps={{
                format: 'HH:mm:ss',
              }}
              // {...(isRequired && { required: true, message: '请选择执行时间' })}
              rules={[
                {
                  required: true,
                  validator(_rule, value, callback) {
                    if (!value) {
                      return callback('请选择执行时间');
                    }

                    const currentTime = dayjs(value, 'HH:mm:ss');
                    const generateTime = dayjs(target?.[generateTimeName], 'HH:mm:ss');

                    if (
                      !target?.[executeDateName] &&
                      (currentTime.isBefore(generateTime) || currentTime.isSame(generateTime))
                    ) {
                      return callback('当执行日期为0天时，执行时间需大于生成时间');
                    }

                    callback();
                  },
                },
              ]}
            />
          );
        }}
      </ProFormDependency>
    </>
  );
}

export const EdgeTriggerTypeFormMap: Record<EdgeTriggerType, (data?: any) => ReactNode> = {
  [EdgeTriggerType.CIRCULATION]: ({
    cycleName,
    timeName,
    rangeName,
    taskActionName,
    generateDateName,
    generateTimeName,
    executeDateName,
    executeTimeName,
  }) => (
    <>
      <ProFormSelect
        label="周期选择"
        name={cycleName}
        valueEnum={StrategyCycleTypeCN}
        rules={[
          {
            required: true,
            message: '请选择周期',
          },
        ]}
      />
      <ProFormSelect
        label="任务执行方式"
        valueEnum={TaskExecuteWayTypeCN}
        name={taskActionName}
        width={190}
        initialValue={TaskExecuteWayEnum.INSTANTLY}
      />
      <ProFormDependency name={[taskActionName]}>
        {(target) => {
          if (target?.[taskActionName] === TaskExecuteWayEnum.CUSTOMIZE) {
            return (
              <CustomizeExecuteTImeConfig
                isRequired
                isCirculate
                generateDateName={generateDateName}
                generateTimeName={generateTimeName}
                executeDateName={executeDateName}
                executeTimeName={executeTimeName}
                cycleName={cycleName}
                rangeName={rangeName}
              />
            );
          }

          return (
            <>
              <ProFormDependency name={[cycleName]}>
                {() => {
                  return (
                    <ProFormDateRangePicker
                      label="日期选择"
                      name={rangeName}
                      fieldProps={{
                        format: 'YYYY-MM-DD',
                      }}
                      rules={[
                        {
                          required: true,
                          message: '请选择日期',
                        },
                      ]}
                    />
                  );
                }}
              </ProFormDependency>
              <ProFormTimePicker
                label="时间选择"
                name={timeName}
                fieldProps={{
                  format: 'HH:mm:ss',
                }}
                rules={[
                  {
                    required: true,
                    message: '请选择时间',
                  },
                ]}
              />
            </>
          );
        }}
      </ProFormDependency>
    </>
  ),
  [EdgeTriggerType.ONCE]: ({
    dateName,
    timeName,
    generateDateName,
    generateTimeName,
    executeDateName,
    executeTimeName,
    taskActionName,
  }) => (
    <>
      <ProFormSelect
        label="任务执行方式"
        valueEnum={TaskExecuteWayTypeCN}
        name={taskActionName}
        width={190}
        initialValue={TaskExecuteWayEnum.INSTANTLY}
      />
      <ProFormDependency name={[taskActionName]}>
        {(target) => {
          if (target?.[taskActionName] === TaskExecuteWayEnum.CUSTOMIZE) {
            return (
              <CustomizeExecuteTImeConfig
                generateDateName={generateDateName}
                generateTimeName={generateTimeName}
                executeDateName={executeDateName}
                executeTimeName={executeTimeName}
              />
            );
          }

          return (
            <>
              <ProFormDatePicker label="日期选择" name={dateName} />
              <ProFormTimePicker
                label="时间选择"
                name={timeName}
                fieldProps={{
                  format: 'HH:mm:ss',
                }}
              />
            </>
          );
        }}
      </ProFormDependency>
    </>
  ),
  // [EdgeTriggerType.Custom]: ({ dateName }) => (
  //   <>
  //     <ProFormTextArea label="自定义接口" name={dateName} />
  //   </>
  // ),
  [EdgeTriggerType.END]: undefined,
  [EdgeTriggerType.EVENT]: ({ eventValueName, eventFromName, strategyNameOptions, safetyFlag }) => (
    <>
      <ProFormSelect
        label="事件来源"
        name={eventFromName}
        showSearch
        options={[
          {
            label: '关联事件生成',
            value: 'STRATEGY',
          },
          {
            label: '开业进度节点',
            value: 'PROGRESS_OF_WORKS',
          },
          {
            label: '中台-人员交接事件',
            value: 'RESIGNATION_HANDOVER',
          },
        ].concat(
          safetyFlag === 'true'
            ? [
                {
                  label: '大数据-食安线下排班事件',
                  value: 'DATA_FOOD_SAFETY_NORMAL',
                },
              ]
            : [],
        )}
        rules={[
          {
            required: true,
            message: '请选择关联事件生成',
          },
        ]}
      />
      <ProFormDependency name={[eventFromName]}>
        {({ event }) => {
          // const isOpeningProgress = event === 'PROGRESS_OF_WORKS';
          const labelMap = { STRATEGY: '策略名称', PROGRESS_OF_WORKS: '开业进度', RESIGNATION_HANDOVER: '交接方' };
          const optionsMap = {
            STRATEGY: strategyNameOptions,
            PROGRESS_OF_WORKS: engineeringProgressOptions,
            RESIGNATION_HANDOVER: partyOptions,
          };

          return ['STRATEGY', 'PROGRESS_OF_WORKS', 'RESIGNATION_HANDOVER'].includes(event) ? (
            <ProFormSelect
              label={labelMap?.[event]}
              name={eventValueName}
              showSearch
              options={optionsMap?.[event]}
              rules={[
                {
                  required: true,
                  message: `请选择${labelMap?.[event]}`,
                },
              ]}
            />
          ) : undefined;
        }}
      </ProFormDependency>
    </>
  ),
  [EdgeTriggerType.HIKVISION_EVENT]: () => {
    return (
      <>
        <ProFormText
          label="海康项目ID"
          name="hikvisionProjectId"
          rules={[
            {
              required: true,
              message: '请输入海康项目ID',
            },
          ]}
        />
        <ProFormText
          label="海康任务ID"
          name="hikvisionTaskId"
          rules={[
            {
              required: true,
              message: '请输入海康任务ID',
            },
          ]}
        />
      </>
    );
  },
};

export interface EdgeTriggerProps {
  form?: ProFormInstance;
  typeName: string;
  listName: string;
  cycleName: string;
  timeName: string;
  rangeName: string;
  dateName: string;
  eventValueName: string;
  eventFromName: string;
  strategyNameOptions: { label: string; value: number }[];
  /** 任务执行方式 */
  taskActionName: string;
  /** 生成日期 */
  generateDateName: string;
  /** 生成时间 */
  generateTimeName: string;
  /** 执行日期 */
  executeDateName: string;
  /** 执行时间 */
  executeTimeName: string;
  onDelete?: (index: number) => void;
  canShowHikTriggerOption?: boolean;
}

const EdgeTrigger: FC<EdgeTriggerProps> = ({
  onDelete,
  typeName,
  listName,
  cycleName,
  timeName,
  rangeName,
  dateName,
  eventValueName,
  eventFromName,
  strategyNameOptions,
  form,
  taskActionName,
  generateDateName,
  generateTimeName,
  executeTimeName,
  executeDateName,
  canShowHikTriggerOption,
}) => {
  const actionRef = useRef<
    FormListActionType<{
      name: string;
    }>
  >();
  const triggers = ProForm.useWatch('triggers', form);

  useEffect(() => {
    eventEmitter.emit('triggerType', triggers?.[0]?.triggerType);
    // 事件来源是否包含大数据排班
    eventEmitter.emit(
      'is-DATA_FOOD_SAFETY_NORMAL-Event',
      triggers?.some((item) => {
        return item?.[eventFromName] === 'DATA_FOOD_SAFETY_NORMAL';
      }),
    );
  }, [eventFromName, triggers]);

  const [params]: any = useQuerySearchParams();

  return (
    <ProFormList
      name={listName}
      copyIconProps={false}
      deleteIconProps={false}
      creatorButtonProps={{
        creatorButtonText: '新增触发条件',
      }}
      actionRef={actionRef}
      alwaysShowItemLabel={true}
      max={1}
    >
      {(_r, index: number, _a, len: number) => {
        return (
          <div className="w-full">
            <ProFormSelect
              label="触发条件类型"
              options={Object.entries(EdgeTriggerTypeCN)
                .filter(([key]) => {
                  // 当 canShowHikTriggerOption 为 false 时，过滤掉海康项目选项
                  if (!canShowHikTriggerOption && key === EdgeTriggerType.HIKVISION_EVENT) {
                    return false;
                  }

                  return true;
                })
                .map(([value, label]) => ({ value, label }))}
              name={typeName}
              width={190}
              rules={[
                {
                  required: true,
                  message: '请选择触发条件类型',
                },
              ]}
            />
            <ProFormDependency name={[typeName]}>
              {(target) => {
                return (
                  <>
                    {EdgeTriggerTypeFormMap?.[target?.[typeName]]?.({
                      cycleName,
                      timeName,
                      rangeName,
                      dateName,
                      eventValueName,
                      eventFromName,
                      strategyNameOptions,
                      taskActionName,
                      generateDateName,
                      generateTimeName,
                      executeDateName,
                      executeTimeName,
                      safetyFlag: params?.safetyFlag,
                    })}
                  </>
                );
              }}
            </ProFormDependency>
            <div className="flex justify-center h-[56px] relative leading-8 ">
              {index < len - 1 && <span>或</span>}

              <Button
                type="link"
                className="absolute right-4"
                disabled={params?.readonly}
                onClick={() => {
                  actionRef?.current?.remove(index);
                  onDelete?.(index);
                }}
              >
                删除
              </Button>
            </div>
          </div>
        );
      }}
    </ProFormList>
  );
};

export default EdgeTrigger;
