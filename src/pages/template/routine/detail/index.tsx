/* eslint-disable max-lines-per-function */
import './index.scss';

import { FC, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { MinusSquareOutlined, PlusSquareOutlined } from '@ant-design/icons';
import {
  ProCard,
  ProForm,
  ProFormCheckbox,
  ProFormDependency,
  ProFormDigit,
  ProFormList,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Checkbox, Form, InputNumber, message, Modal, Space, Table } from 'antd';
import { cloneDeep } from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import { DifferenceMappingModal } from './components/DifferenceMappingModal';
import { DifferenceTableMapping } from './components/DifferenceTableMapping';
import Service, { RequestName } from './service';
import ChecklistList from '../../components/fields/checklist';
import TaskExecutionDuration, { genMonthDurationOptions } from '../../components/fields/task-execution-duration';
import DiagnosisTable, { type DiagnosisTableRef } from '../../diagnosis/components/DiagnosisTable';
import DiagnosisConfigBindStore from '../../diagnosis/components/store/DiagnosisConfigBindStore';
import { ChecklistType } from '@/constants/checklist';
import { ChecklistStrategyType } from '@/constants/checklist-strategy';
import { PackConfigType, PackConfigTypeCN, PatrolType, SignType, SignTypeCN } from '@/constants/patrol';
import {
  StrategyRoutineType,
  StrategyRoutineTypeCN,
  StrategyVideoPatrolType,
  StrategyVideoPatrolTypeCN,
} from '@/constants/strategy';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import ChecklistItemsSelectModal from '@/pages/parameter/components/checklist-items-select-modal';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { renderPage } from '@/utils/render';

export enum RoutineTemplateDetailType {
  Add = 'Add', // 新增
  Readonly = 'Readonly', // 只读
  Edit = 'Edit', // 编辑
  Copy = 'Copy', // 复制
}

const regex = /\d+/g;

const RoutineTemplateDetail: FC = () => {
  const [form] = ProForm.useForm();
  const [params] = useQuerySearchParams();
  const navigate = useGlobalNavigate();
  const taskType = ProForm.useWatch('taskType', form);
  const signType = ProForm.useWatch('signType', form);
  const [commonChecklistOptions, setCommonChecklistOptions] = useState<any>([]);

  const worksheet = ProForm.useWatch('worksheetIds', form);
  const videoPatrolType = ProForm.useWatch('videoPatrolType', form);
  const executeDuration = ProForm.useWatch('executeDuration', form);
  const optionsNum = useMemo(() => worksheet?.filter(({ sheet }) => sheet.selectorSwitch).length ?? 0, [worksheet]);
  const diagnosisTableRef = useRef<DiagnosisTableRef>(null);
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);

  const [itemsModalProps, setItemsModalProps] = useState<{
    open: boolean;
    title?: string;
    checklistLabel?: string;
    worksheetName?: string;
    worksheetCategoryName?: string;
    worksheetItemName?: string;
    subIndex?: number;
  }>({
    open: false,
  });

  const [lastDayFlag, setLastDayFlag] = useState(false);

  const [service, executeRequest] = Service();

  useEffect(() => {
    requestChecklist().then((res) => {
      setCommonChecklistOptions(res);
    });

    executeRequest(RequestName.GetTags);

    if (params?.id) {
      executeRequest(RequestName.GetRoutineTemplateDetail, params?.id).then((res) => {
        const {
          worksheetIds,
          executeDuration,
          templateName,
          shopDurations,
          executeTimeModelType,
          executeTime,
          taskType,
          ...response
        } = res;

        const duration = executeDuration?.match(regex);

        const shopDuration = shopDurations?.map((item) => {
          const duration = item?.executeDuration?.match(regex);

          return {
            shopType: item.patrolShopType,
            executeTimeModelType: item?.executeTimeModelType === 'END_OF_MONTH' ? true : undefined,
            executeDuration: item?.executeDuration ? [Math.floor(duration / 24), duration % 24] : undefined,
          };
        });

        form?.setFieldsValue({
          ...response,
          taskType,
          templateName:
            params?.operateType === RoutineTemplateDetailType.Copy ? `${templateName} 【副本】` : templateName,
          worksheetIds: worksheetIds?.map(({ worksheetId, totalScore, weight, must }) => ({
            sheet: { checklist: worksheetId, selectorSwitch: Number(must) },
            weight,
            totalScore,
          })),
          tags: shopDuration,
          executeDuration:
            executeTimeModelType !== 'END_OF_MONTH'
              ? taskType !== StrategyRoutineType.DIAGNOSTIC
                ? executeTime?.length > 0
                  ? executeTime?.slice(0, 3)
                  : [0, Math.floor(duration / 24), duration % 24]
                : [Math.floor(duration / 24), duration % 24]
              : undefined,

          worksheetItems: res?.worksheetItems?.map(({ refItems, sourceItem }) => ({
            ...sourceItem,
            worksheetItemName: sourceItem.content,
            worksheetItemId: sourceItem?.id,
            worksheetType: sourceItem?.itemType,
            refItems: refItems.map((item) => ({
              ...item,
              worksheetItemName: item?.content,
              worksheetItemId: item?.id,
            })),
          })),
          differenceItemConfigs: res?.differenceItemConfigDTOList?.length
            ? res?.differenceItemConfigDTOList?.map((dto) => {
                return {
                  differenceItemConfigs: {
                    id: uuidv4(),
                    differenceItemId: {
                      label: dto?.differenceItemName,
                      value: dto?.differenceItemId,
                    },
                    differenceWorksheetIds: dto?.differenceWorksheetConfigs?.map((item) => {
                      return {
                        workSheetId: item?.worksheetId,
                        sheet: {
                          checklist: item?.worksheetId,
                          selectorSwitch: 0,
                        },
                      };
                    }),
                    differenceWorksheetLabels: dto?.differenceWorksheetConfigs?.map((item) => {
                      return item?.worksheetName;
                    }),
                  },
                };
              })
            : undefined,
        });

        setLastDayFlag(executeTimeModelType === 'END_OF_MONTH');
      });
    }
  }, []);

  useLayoutEffect(() => {
    document.addEventListener('diagnoseTaskConfig', (res: any) => {
      !!res?.detail && form.setFieldsValue({ ...res?.detail });
    });

    return () => {
      document.removeEventListener('diagnoseTaskConfig', () => {});
    };
  }, []);

  useEffect(() => {
    if (params?.isEdit === 'true') {
      return;
    }

    DiagnosisConfigBindStore.resetDiagnosisConfig();
  }, []);

  const requestChecklist = (labelIds?: number[]) => {
    return executeRequest(RequestName.GetChecklistSimplelist, {
      labelIds,
      worksheetTypes: [ChecklistStrategyType.PATROL],
    });
  };

  const disabled = useMemo(() => {
    if (!params?.operateType || params?.operateType === RoutineTemplateDetailType.Readonly) {
      return true;
    }

    return false;
  }, [params?.operateType]);

  const checkDifferenceItemConfigs = (_: any, value?: any[]) => {
    if (!!value?.length) {
      return Promise.resolve();
    }

    return Promise.reject(new Error('请配置差异项映射'));
  };

  return (
    <>
      <ProCard className="min-w-[800px] flex">
        <ProForm
          layout="horizontal"
          form={form}
          labelAlign="right"
          disabled={disabled}
          className="routine-template-detail-form mt-2"
          submitter={{
            render: ({ form }) => {
              return (
                !disabled && (
                  <Button
                    className="ml-[140px]"
                    onClick={() => {
                      form?.submit?.();
                    }}
                    type="primary"
                  >
                    提交
                  </Button>
                )
              );
            },
          }}
          onFinish={(values) => {
            const diagnostics = diagnosisTableRef.current?.getConfig();

            const { worksheetIds, executeDuration, id, mustCount, tags, worksheetItems, ...value } = values;

            const data = {
              ...value,
              id: params?.operateType === RoutineTemplateDetailType.Copy ? undefined : id,
              worksheetIds: worksheetIds?.map(({ sheet, weight }) => ({
                worksheetId: sheet?.checklist,
                weight: weight || 0,
                must: !!sheet?.selectorSwitch,
              })),
              shopDurations: tags?.map(({ executeDuration, shopType, executeTimeModelType }) => ({
                executeTimeModelType: executeTimeModelType ? 'END_OF_MONTH' : undefined,
                patrolShopType: shopType,
                executeDuration:
                  executeDuration?.length &&
                  `P${executeDuration[0] > 0 ? `${executeDuration[0]}D` : ''}${executeDuration[1] >= 0 ? `T${executeDuration[1]}H` : ''}`,
              })),
              executeTime:
                !lastDayFlag && taskType !== StrategyRoutineType.DIAGNOSTIC && executeDuration?.length > 0
                  ? [...executeDuration, 0]
                  : undefined,

              temporarilyTemplateId:
                params?.operateType === 'Add' && taskType === PatrolType.DIAGNOSTIC
                  ? params?.temporarilyTemplateId
                  : undefined,
              executeDuration: lastDayFlag
                ? undefined
                : taskType === StrategyRoutineType.DIAGNOSTIC && executeDuration?.length
                  ? `P${executeDuration[0] > 0 ? `${executeDuration[0]}D` : ''}${executeDuration[1] >= 0 ? `T${executeDuration[1]}H` : ''}`
                  : undefined,

              diagnostics: taskType === PatrolType.DIAGNOSTIC ? diagnostics : undefined,
              mustCount: Number(mustCount),
              worksheetItemIds: worksheetItems?.map(({ worksheetId, worksheetItemId, worksheetType, refItems }) => ({
                worksheetId,
                worksheetItemId,
                worksheetType,
                bindWorksheetItemIds: refItems?.map(({ worksheetItemId }) => worksheetItemId),
              })),
              executeTimeModelType: lastDayFlag ? 'END_OF_MONTH' : undefined,
              differenceItemConfigs: value?.differenceItemConfigs?.map((item) => {
                return {
                  differenceItemId: item?.differenceItemConfigs?.differenceItemId?.value,
                  differenceWorksheetIds: item?.differenceItemConfigs?.differenceWorksheetIds?.map(
                    ({ sheet }) => sheet?.checklist,
                  ),
                };
              }),
            };

            if (optionsNum && optionsNum <= 1) {
              message.error('选填至少需要有2个');

              return;
            }

            if ([PackConfigType.NEED_PACKAGE, PackConfigType.NOT_NEED_PACKAGE].includes(values?.packageConfig)) {
              return Modal.warning({
                title: '提示',
                content: `确认该任务${PackConfigTypeCN[values?.packageConfig]}被打包`,
                onOk: async () => {
                  await executeRequest(RequestName.SaveRoutineTemplate, data)
                    .then(() => {
                      navigate(-1);
                    })
                    .catch(() => {
                      Promise.reject();
                    });
                },
              });
            }

            return executeRequest(RequestName.SaveRoutineTemplate, data).then(() => {
              navigate(-1);
            });
          }}
        >
          <ProForm.Item name="id" hidden />
          <ProFormText
            label="任务模板名称"
            name="templateName"
            width="md"
            placeholder="请输入任务模板名称"
            rules={[
              {
                required: true,
                message: '请输入任务模板名称',
              },
            ]}
          />
          <ProFormRadio.Group
            label="巡检类型"
            name="taskType"
            layout="vertical"
            fieldProps={{
              onChange: () => {
                form.resetFields(['executeDuration']);
              },
            }}
            rules={[
              {
                required: true,
                message: '请选择巡检类型',
              },
            ]}
            initialValue={StrategyRoutineType.NORMAL}
            valueEnum={StrategyRoutineTypeCN}
          />
          <ProFormDependency name={['taskType']}>
            {({ taskType }) => {
              return (
                taskType === StrategyRoutineType.VIDEO && (
                  <ProFormRadio.Group
                    label="云巡检任务类型"
                    name="videoPatrolType"
                    layout="vertical"
                    rules={[
                      {
                        required: true,
                        message: '请选择巡检类型',
                      },
                    ]}
                    initialValue={StrategyVideoPatrolType.ONLINE_SPECIALIST}
                    valueEnum={{
                      [StrategyVideoPatrolType.ONLINE_SPECIALIST]:
                        StrategyVideoPatrolTypeCN[StrategyVideoPatrolType.ONLINE_SPECIALIST],
                      [StrategyVideoPatrolType.ONLINE_SUPERVISE]:
                        StrategyVideoPatrolTypeCN[StrategyVideoPatrolType.ONLINE_SUPERVISE],
                    }}
                  />
                )
              );
            }}
          </ProFormDependency>

          {[StrategyRoutineType.FOOD_SAFETY_ARRIVE_SHOP].includes(taskType) ? (
            <>
              <ProFormList
                name="tags"
                creatorButtonProps={false}
                initialValue={[
                  { shopType: 'ABNORMAL', executeTimeModelType: undefined },
                  {
                    shopType: 'RECHECK',
                    executeTimeModelType: undefined,
                  },
                ]}
                alwaysShowItemLabel={true}
                actionRender={() => {
                  return (<></>) as any; // 固定两个-隐藏操作按钮
                }}
              >
                {(_, index) => {
                  return (
                    <>
                      <div className="flex">
                        <ProFormSelect
                          label="门店类型"
                          name="shopType"
                          disabled
                          valueEnum={{
                            ABNORMAL: '不合格门店',
                            RECHECK: '复检门店',
                          }}
                          rules={[
                            {
                              required: true,
                              message: '请选择门店类型',
                            },
                          ]}
                          width="md"
                        />
                        <Space>
                          <ProFormDependency name={['tags', index, 'executeTimeModelType']}>
                            {(_, { getFieldValue }) => {
                              const val = getFieldValue(['tags', index, 'executeTimeModelType']);

                              return <TaskExecutionDuration form={form} disabled={val} />;
                            }}
                          </ProFormDependency>
                          <ProFormDependency name={['tags', index, 'executeDuration']}>
                            {(_, { getFieldValue }) => {
                              const val = getFieldValue(['tags', index, 'executeDuration']);

                              return (
                                <ProFormCheckbox name="executeTimeModelType" disabled={val?.length}>
                                  默认每个月最后一天24:00结束
                                </ProFormCheckbox>
                              );
                            }}
                          </ProFormDependency>
                        </Space>
                      </div>
                    </>
                  );
                }}
              </ProFormList>
              {!disabled && (
                <a
                  onClick={() => {
                    setItemsModalProps({
                      open: true,
                      title: '选择指标源检查项',
                      checklistLabel: '检查表',
                    });
                  }}
                >
                  + 选择指标源检查项
                </a>
              )}
              <ProForm.Item
                name="worksheetItems"
                // hidden={true}
                rules={[
                  {
                    validator: (_, value) => {
                      const noRefItemNames: string[] = [];

                      value?.forEach(({ refItems, worksheetName }) => {
                        if (!refItems?.length) {
                          noRefItemNames.push(worksheetName);
                        }
                      });

                      if (!!noRefItemNames?.length) {
                        return Promise.reject(`请绑定指标源检查表：${noRefItemNames.join('、')} 的关联诊断任务检查表`);
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <ProFormDependency name={['worksheetItems']}>
                  {({ worksheetItems }) => {
                    return (
                      worksheetItems?.length > 0 && (
                        <Table
                          className="my-4 gzt-diagnosis-table"
                          pagination={false}
                          rowKey={'worksheetItemId'}
                          dataSource={worksheetItems}
                          columns={[
                            {
                              title: '检查表',
                              dataIndex: 'worksheetName',
                              // width: 200,
                            },
                            {
                              title: '检查项',
                              dataIndex: 'worksheetItemName',
                              width: 500,
                              render: (_, record) => record?.worksheetItemName,
                            },
                            {
                              title: '操作',
                              dataIndex: 'operate',
                              width: 120,
                              render: (_, record, index) => {
                                if (disabled) {
                                  return;
                                }

                                return (
                                  <Space>
                                    <a
                                      onClick={() => {
                                        setItemsModalProps({
                                          title: '检查项内容配置',
                                          open: true,
                                          checklistLabel: '绑定映射检查表',

                                          worksheetName: record?.worksheetName,
                                          worksheetCategoryName: record?.worksheetCategoryName,
                                          worksheetItemName: record?.worksheetItemName,
                                          subIndex: index,
                                        });
                                      }}
                                    >
                                      去配置
                                    </a>
                                    <a
                                      className="text-[#EA0000]"
                                      onClick={() => {
                                        Modal.confirm({
                                          title: '删除指标源检查项',
                                          icon: null,
                                          content: <p className="my-4">是否确认删除该指标源检查项？</p>,
                                          okText: '确定',
                                          cancelText: '取消',
                                          closable: true,
                                          onOk: () => {
                                            const worksheetItemsClone = cloneDeep(worksheetItems);

                                            worksheetItemsClone.splice(index, 1);
                                            form?.setFieldValue('worksheetItems', worksheetItemsClone);
                                            setExpandedRowKeys(
                                              expandedRowKeys.filter((id) => id !== record?.worksheetItemId),
                                            );
                                          },
                                        });
                                      }}
                                    >
                                      删除
                                    </a>
                                  </Space>
                                );
                              },
                            },
                          ]}
                          expandable={{
                            expandedRowKeys,
                            expandIcon: ({ record, onExpand, expanded }) => {
                              return record?.refItems?.length > 0 ? (
                                expanded ? (
                                  <MinusSquareOutlined
                                    onClick={() => {
                                      setExpandedRowKeys(
                                        expandedRowKeys.filter((id) => id !== record?.worksheetItemId),
                                      );
                                    }}
                                  />
                                ) : (
                                  <PlusSquareOutlined
                                    onClick={() => {
                                      setExpandedRowKeys(expandedRowKeys.concat([record?.worksheetItemId]));
                                    }}
                                  />
                                )
                              ) : null;
                            },
                            expandedRowRender: (record, rowIndex, _, expanded) => {
                              const { refItems, worksheetItemId } = record;

                              return (
                                refItems &&
                                refItems?.length > 0 && (
                                  <Table
                                    className="my-4 gzt-diagnosis-table"
                                    rowKey={'worksheetItemId'}
                                    dataSource={refItems}
                                    pagination={false}
                                    columns={[
                                      {
                                        title: '关联诊断任务检查表',
                                        dataIndex: 'worksheetName',
                                        // width: 200,
                                      },
                                      {
                                        title: '检查项',
                                        dataIndex: 'worksheetItemName',
                                        width: 500,
                                        render: (_, record) => record?.worksheetItemName,
                                      },
                                      {
                                        title: '操作',
                                        dataIndex: 'operate',
                                        width: 120,
                                        render: (_, record, index) => {
                                          if (disabled) {
                                            return;
                                          }

                                          return (
                                            <Space>
                                              <a
                                                className="text-[#EA0000]"
                                                onClick={() => {
                                                  Modal.confirm({
                                                    title: '删除绑定映射关系',
                                                    icon: null,
                                                    content: (
                                                      <p className="my-4">是否确认删除该检查项的绑定映射关系？</p>
                                                    ),
                                                    okText: '确定',
                                                    cancelText: '取消',
                                                    closable: true,
                                                    onOk: () => {
                                                      const refItemsClone = cloneDeep(refItems);
                                                      const worksheetItemsClone = cloneDeep(worksheetItems);

                                                      refItemsClone.splice(index, 1);
                                                      worksheetItemsClone[rowIndex].refItems = refItemsClone;
                                                      form?.setFieldValue('worksheetItems', worksheetItemsClone);

                                                      // 删除子表时，如果子表只有一条数据,删除成功同时收起子表
                                                      if (refItemsClone.length === 0) {
                                                        setExpandedRowKeys(
                                                          expandedRowKeys.filter((id) => id !== worksheetItemId),
                                                        );
                                                      }
                                                    },
                                                  });
                                                }}
                                              >
                                                删除
                                              </a>
                                            </Space>
                                          );
                                        },
                                      },
                                    ]}
                                  />
                                )
                              );
                            },
                          }}
                        />
                      )
                    );
                  }}
                </ProFormDependency>
              </ProForm.Item>
            </>
          ) : (
            <>
              {[StrategyRoutineType.DIAGNOSTIC].includes(taskType) && (
                <DiagnosisTable
                  ref={diagnosisTableRef}
                  form={form}
                  dataSource={service?.diagnosticItemList || service?.temporaryDiagnosticItemList}
                />
              )}
              {![
                StrategyRoutineType.DIFFERENCE_ITEM_ARRIVE_SHOP,
                StrategyRoutineType.FOOD_SAFETY_ARRIVE_SHOP,
                StrategyRoutineType.HANDOVER,
              ].includes(taskType) && (
                <Space align="start" className="relative">
                  <TaskExecutionDuration
                    disabled={lastDayFlag}
                    form={form}
                    options={taskType !== StrategyRoutineType.DIAGNOSTIC ? genMonthDurationOptions() : undefined}
                  />
                  {videoPatrolType !== StrategyVideoPatrolType.ONLINE_SUPERVISE && (
                    <Checkbox
                      checked={lastDayFlag}
                      className="mt-1.5"
                      disabled={executeDuration?.length}
                      onChange={(e) => {
                        setLastDayFlag(e.target.checked);
                        form.setFieldsValue({
                          executeDuration: [],
                        });
                      }}
                    >
                      默认每个月最后一天24:00结束
                    </Checkbox>
                  )}
                </Space>
              )}
              {[StrategyRoutineType.DIFFERENCE_ITEM_ARRIVE_SHOP].includes(taskType) && (
                <Form.Item name="differenceItemConfigs" rules={[{ validator: checkDifferenceItemConfigs }]}>
                  <DifferenceTableMapping />
                </Form.Item>
              )}
              {![StrategyRoutineType.DIAGNOSTIC, StrategyRoutineType.DIFFERENCE_ITEM_ARRIVE_SHOP].includes(
                taskType,
              ) && (
                <>
                  <ChecklistList
                    form={form}
                    name="worksheetIds"
                    showWeight={true}
                    disabled={disabled}
                    requestChecklist={requestChecklist}
                    tagOptions={service?.tagOptions}
                    checklistOptions={commonChecklistOptions}
                  />
                  {optionsNum > 1 && (
                    <ProForm.Item
                      label="选填最小完成"
                      name="mustCount"
                      rules={[
                        {
                          required: true,
                          message: '请输入选填最小完成',
                        },
                        {
                          validator: (_, value) => {
                            const num = optionsNum - 1;

                            if (optionsNum && value > num) {
                              return Promise.reject(`选填个数不能大于${num}`);
                            }

                            return Promise.resolve();
                          },
                        },
                      ]}
                    >
                      <InputNumber min={1} max={100} />
                    </ProForm.Item>
                  )}
                </>
              )}
            </>
          )}
          {/* <ProFormDependency name={['taskType']}>
            {({ taskType }) => {
              return (
                [StrategyRoutineType.VIDEO, StrategyRoutineType.FOOD_SAFETY_VIDEO].includes(taskType) && (
                  <ProFormDigit
                    label="每周巡检门店数下限"
                    name="videoPatrolLimit"
                    fieldProps={{ precision: 0, min: 0, suffix: '个' }}
                    width="xs"
                    rules={[
                      {
                        required: true,
                        message: '请输入每周巡检门店数下限',
                      },
                    ]}
                  />
                )
              );
            }}
          </ProFormDependency> */}

          {[StrategyRoutineType.FOOD_SAFETY_NORMAL].includes(taskType) && (
            <>
              <ProFormRadio.Group
                name="allowOverdue"
                label="任务是否可逾期执行"
                options={[
                  {
                    label: '不可逾期执行',
                    value: false,
                  },
                  {
                    label: '可逾期执行',
                    value: true,
                  },
                ]}
                initialValue={false}
                tooltip="仅对大数据算法生效"
              />
              <ProFormDependency name={['allowOverdue']}>
                {({ allowOverdue }) => {
                  return (
                    allowOverdue && (
                      <ProForm.Item label="可逾期时长">
                        <div className="flex flex-row">
                          <ProFormDigit
                            name={'allowOverdueDuration'}
                            width="xs"
                            rules={[
                              {
                                required: true,
                                message: '请输入可逾期时长',
                              },
                            ]}
                            noStyle
                            initialValue={2}
                            min={1}
                            max={31}
                          />
                          <ProFormSelect
                            name={'allowOverdueUnit'}
                            width="xs"
                            options={[
                              {
                                label: '天',
                                value: 'DAYS',
                              },
                              {
                                label: '小时',
                                value: 'HOURS',
                              },
                            ]}
                            noStyle
                            initialValue={'DAYS'}
                          />
                        </div>
                      </ProForm.Item>
                    )
                  );
                }}
              </ProFormDependency>
            </>
          )}

          {[
            StrategyRoutineType.NORMAL,
            StrategyRoutineType.FOOD_SAFETY_NORMAL,
            StrategyRoutineType.FOOD_SAFETY_ARRIVE_SHOP,
            StrategyRoutineType.DIAGNOSTIC,
            StrategyRoutineType.DIFFERENCE_ITEM_ARRIVE_SHOP,
            StrategyRoutineType.HANDOVER,
          ].includes(taskType) && (
            <ProFormRadio.Group
              label="是否需要签到/签离"
              required
              // disabled={readonly || disabled}
              name="signType"
              initialValue={SignType.SYSTEM}
              extra={signType === SignType.SYSTEM && '即与系统设置保持一致，不进行单独设置'}
              options={Object.keys(SignTypeCN).map((key) => ({ label: SignTypeCN[key], value: key }))}
            />
          )}
          {[
            StrategyRoutineType.NORMAL,
            StrategyRoutineType.FOOD_SAFETY_ARRIVE_SHOP,
            StrategyRoutineType.DIAGNOSTIC,
            StrategyRoutineType.DIFFERENCE_ITEM_ARRIVE_SHOP,
          ].includes(taskType) && (
            <ProFormRadio.Group
              label="是否需要打包"
              rules={[
                {
                  required: true,
                  message: '请选择是否需要打包',
                },
              ]}
              name="packageConfig"
              initialValue={PackConfigType.FOLLOWING_SYSTEM}
              options={Object.keys(PackConfigTypeCN).map((key) => ({ label: PackConfigTypeCN[key], value: key }))}
            />
          )}
          {/* 只有诊断任务和食安稽核到店辅导没有分数设置 */}
          {![
            StrategyRoutineType.DIAGNOSTIC,
            StrategyRoutineType.FOOD_SAFETY_ARRIVE_SHOP,
            StrategyRoutineType.DIFFERENCE_ITEM_ARRIVE_SHOP,
          ].includes(taskType) && (
            <ProFormDigit
              label="通过分数设置"
              min={0}
              max={100}
              initialValue={100}
              // disabled={readonly || disabled}
              placeholder="请输入通过分数线,满分100分"
              fieldProps={{ precision: 0 }}
              name="passScore"
              width="sm"
            />
          )}
          {([StrategyRoutineType.NORMAL].includes(taskType) ||
            (StrategyRoutineType.VIDEO === taskType &&
              videoPatrolType === StrategyVideoPatrolType.ONLINE_SUPERVISE)) && (
            <ProFormDigit
              label="同个任务模板"
              min={1}
              max={365}
              fieldProps={{ precision: 0 }}
              addonAfter="天去重"
              name="qcDays"
              width="sm"
            />
          )}
          <ProFormDependency name={['taskType', 'reportItemFlag']}>
            {({ taskType, reportItemFlag }) => {
              return (
                [
                  StrategyRoutineType.NORMAL,
                  StrategyRoutineType.VIDEO,
                  StrategyRoutineType.FOOD_SAFETY_VIDEO,
                  StrategyRoutineType.FOOD_SAFETY_NORMAL,
                ].includes(taskType) && (
                  // <ProFormCheckbox name="" label="是否新增提报项" initialValue={false} width="sm" />
                  <ProForm.Item name="reportItemFlag">
                    <Checkbox
                      className="ml-[160px]"
                      checked={reportItemFlag}
                      onChange={(e) => {
                        form.setFieldValue('reportItemFlag', e.target.checked);
                      }}
                    >
                      是否新增提报项
                    </Checkbox>
                  </ProForm.Item>
                )
              );
            }}
          </ProFormDependency>
        </ProForm>
        <ChecklistItemsSelectModal
          worksheetName={itemsModalProps.worksheetName}
          worksheetCategoryName={itemsModalProps.worksheetCategoryName}
          worksheetItemName={itemsModalProps.worksheetItemName}
          title={itemsModalProps?.title}
          checklistLabel={itemsModalProps.checklistLabel}
          open={itemsModalProps?.open}
          tagOptions={service?.tagOptions}
          checklistOptions={service?.checklistOptions}
          itemsOptions={service?.itemsOptions}
          onTagChange={(values) => {
            requestChecklist(values);
          }}
          onChecklistChange={(value) => {
            if (value?.value) {
              if (typeof value?.value === 'string' && value?.value?.includes('-')) {
                executeRequest(RequestName.GetChecklistCategoriesAndItems, value?.value?.split('-')[1]);
              } else {
                executeRequest(RequestName.GetChecklistCategoriesAndItems, value?.value);
              }
            }
          }}
          onOk={function (values: any) {
            const format = (values) => {
              const { worksheet, items } = values;

              return items?.map((item) => ({
                worksheetName: worksheet?.checklist?.label,
                worksheetId:
                  typeof worksheet?.checklist?.value === 'string' && worksheet?.checklist?.value?.includes('-')
                    ? worksheet?.checklist?.value.split('-')[1]
                    : worksheet?.checklist?.value,
                worksheetCategoryId: item[0]?.value,
                worksheetCategoryName: item[0]?.label,
                worksheetItemId: item[1]?.value,
                worksheetItemName: item[1]?.label,
                worksheetType:
                  typeof worksheet?.checklist?.value === 'string' && worksheet?.checklist?.value?.includes('-')
                    ? worksheet?.checklist?.value?.split('-')[0]
                    : ChecklistType.PATROL,
              }));
            };
            const validateRepeat = (target, origin) => {
              const worksheetIds = [];
              const worksheetItemIds = [];

              origin?.forEach(({ worksheetId, worksheetItemId }) => {
                worksheetIds.push(worksheetId);
                worksheetItemIds.push(worksheetItemId);
              });

              const repeat = [];

              target?.forEach(({ worksheetId, worksheetItemId, worksheetItemName }) => {
                if (worksheetIds?.includes(worksheetId) && worksheetItemIds.includes(worksheetItemId)) {
                  repeat.push(worksheetItemName);
                }
              });

              return repeat;
            };

            const worksheetItemsClone = cloneDeep(form?.getFieldValue('worksheetItems')) || [];

            const newValues = format(values);

            if (itemsModalProps?.subIndex >= 0) {
              const refItems = worksheetItemsClone[itemsModalProps?.subIndex].refItems || [];
              const repeat = validateRepeat(refItems, newValues);

              if (repeat?.length > 0) {
                return message.error(`映射检查项${repeat?.join('、')}重复`);
              }

              worksheetItemsClone[itemsModalProps?.subIndex].refItems = refItems.concat(newValues);
              form.setFieldsValue({
                worksheetItems: worksheetItemsClone,
              });
            } else {
              const repeat = validateRepeat(worksheetItemsClone, newValues);

              if (repeat?.length > 0) {
                return message.error(`指标源检查项${repeat?.join('、')}重复`);
              }

              form.setFieldsValue({
                worksheetItems: worksheetItemsClone.concat(newValues),
              });
            }

            setItemsModalProps({
              open: false,
            });
          }}
          onCancel={() => {
            setItemsModalProps({
              open: false,
            });
          }}
        />
        <DifferenceMappingModal />
      </ProCard>
    </>
  );
};

export default renderPage(RoutineTemplateDetail);
