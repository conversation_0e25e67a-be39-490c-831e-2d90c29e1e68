import '../../style/index.scss';

import { FC, useEffect, useMemo } from 'react';
import {
  ProCard,
  ProForm,
  ProFormCascader,
  ProFormDependency,
  ProFormList,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Button } from 'antd';
import Service, { RequestName } from '../service';
import RoleCascader from '@/components/role-cascader';
import { StrategyPatrolType, StrategyPatrolTypeCN } from '@/constants/strategy';
import { TaskRepeatType } from '@/constants/task';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { genDurationOptions } from '@/pages/template/components/fields/task-execution-duration';
import { regex } from '@/pages/template/self/detail';
import useGlobalNavigate from '@/router/useGlobalNavigate';

export enum TransferTemplateDetailType {
  Add = 'Add', // 新增
  Readonly = 'Readonly', // 只读
  Edit = 'Edit', // 编辑
  Copy = 'Copy', // 复制
}

// eslint-disable-next-line max-lines-per-function
const TransferDetail: FC = () => {
  const [form] = ProForm.useForm();
  const [service, executeRequest] = Service();
  const [params] = useQuerySearchParams();
  const navigate = useGlobalNavigate();

  const taskType = ProForm.useWatch('taskType', form);

  const needAudit = ProForm.useWatch('needAudit', form);
  const supportOutPermission = ProForm.useWatch('supportOutPermission', form);

  const adjustTaskExecuteTimeFlag = ProForm.useWatch('adjustTaskExecuteTimeFlag', form);

  useEffect(() => {
    executeRequest(RequestName.GetRoleCascaderData, 1);

    if (params?.id) {
      executeRequest(RequestName.GetTransferTemplateDetail, params?.id).then((res) => {
        console.log();

        const {
          templateName,
          auditAddExecuteDuration,
          auditExecuteDuration,
          acceptExecuteDuration,
          id,
          processorRoleIds,
        } = res || {};
        const addDuration = auditAddExecuteDuration?.match(regex);
        const executeDuration = auditExecuteDuration?.match(regex);
        const acceptDuration = acceptExecuteDuration?.match(regex);

        form?.setFieldsValue({
          ...res,
          processorRoleIds: processorRoleIds?.map((roles) => ({ roles })),
          auditAddExecuteDuration: [Math.floor(addDuration / 24), addDuration % 24],
          auditExecuteDuration: auditExecuteDuration
            ? [Math.floor(executeDuration / 24), executeDuration % 24]
            : undefined,
          acceptExecuteDuration: acceptExecuteDuration
            ? [Math.floor(acceptDuration / 24), acceptDuration % 24]
            : undefined,
          id: params?.operateType === TransferTemplateDetailType.Edit ? id : undefined,
          templateName:
            params?.operateType === TransferTemplateDetailType.Copy ? `${templateName} 【副本】` : templateName,
        });
      });
    }
  }, [params]);

  const disabled = useMemo(() => {
    if (!params?.operateType || params?.operateType === TransferTemplateDetailType.Readonly) {
      return true;
    }

    return false;
  }, [params?.operateType]);

  const isShowProcessor: Boolean = useMemo(() => {
    return taskType === 'REVIEW' || (taskType === 'PATROL' && supportOutPermission);
  }, [taskType, supportOutPermission]);

  return (
    <>
      <ProCard className="flex">
        <ProForm
          layout="horizontal"
          className="template-detail-form mt-2"
          disabled={disabled}
          submitter={{
            render: ({ form }) => {
              return (
                !disabled && (
                  <Button
                    className="flex ml-[140px]"
                    type="primary"
                    onClick={() => {
                      form?.submit?.();
                    }}
                  >
                    提交
                  </Button>
                )
              );
            },
          }}
          form={form}
          onFinish={(values) => {
            const { id, processorRoleIds, auditAddExecuteDuration, auditExecuteDuration, acceptExecuteDuration } =
              values;

            console.log(values, '=values');

            const formatDoRoleIds = processorRoleIds?.map(({ roles }) => roles);

            const data = {
              ...values,
              processorRoleIds: formatDoRoleIds,
              auditAddExecuteDuration:
                auditAddExecuteDuration?.length &&
                `P${auditAddExecuteDuration[0] > 0 ? `${auditAddExecuteDuration[0]}D` : ''}${auditAddExecuteDuration[1] >= 0 ? `T${auditAddExecuteDuration[1]}H` : ''}`,
              auditExecuteDuration:
                auditExecuteDuration?.length &&
                `P${auditExecuteDuration[0] > 0 ? `${auditExecuteDuration[0]}D` : ''}${auditExecuteDuration[1] >= 0 ? `T${auditExecuteDuration[1]}H` : ''}`,
              acceptExecuteDuration:
                acceptExecuteDuration?.length &&
                `P${acceptExecuteDuration[0] > 0 ? `${acceptExecuteDuration[0]}D` : ''}${acceptExecuteDuration[1] >= 0 ? `T${acceptExecuteDuration[1]}H` : ''}`,
            };

            return executeRequest(
              id ? RequestName.UpdateTransferTemplate : RequestName.SaveTransferTemplate,
              data,
            ).then(() => {
              navigate(-1);
            });
          }}
        >
          <ProForm.Item name="id" hidden />
          <ProFormText
            label="条件模板名称"
            name="templateName"
            width="md"
            placeholder="请输入条件模板名称"
            rules={[
              {
                required: true,
                message: '请输入条件模板名称',
              },
            ]}
          />
          <div className="flex gap-x-2">
            <ProFormSelect
              label="转派任务类型"
              width="md"
              labelCol={{ offset: 2 }}
              placeholder="请选择转派任务类型"
              name="taskType"
              rules={[
                {
                  required: true,
                  message: '请选择转派任务类型',
                },
              ]}
              valueEnum={{ PATROL: '巡检', REVIEW: '点评' }}
            />
            {taskType === 'PATROL' && (
              <ProFormSelect
                width="md"
                name="taskSubType"
                placeholder="请选择巡检类型"
                rules={[
                  {
                    required: true,
                    message: '请选择巡检类型',
                  },
                ]}
                valueEnum={StrategyPatrolTypeCN}
              />
            )}
          </div>
          <ProFormRadio.Group
            name="supportOutPermission"
            label="是否支持权限外"
            initialValue={false}
            fieldProps={{
              onChange: (e) => {
                console.log(e?.target?.value, '=e');
                form.setFieldValue('needAudit', e?.target?.value ? true : undefined);
              },
            }}
            rules={[
              {
                required: true,
                message: '请选择是否支持权限外',
              },
            ]}
            options={[
              {
                label: '否',
                value: false,
              },
              {
                label: '是',
                value: true,
              },
            ]}
          />
          {supportOutPermission && (
            <ProFormCascader
              label="权限外接受时长"
              name="acceptExecuteDuration"
              width="sm"
              rules={[{ required: true, message: '请选权限外接受时长' }]}
              fieldProps={{
                allowClear: true,
                placeholder: '请选择权限外接受时长',
                options: genDurationOptions(TaskRepeatType.MONTH),
              }}
            />
          )}

          {taskType === 'PATROL' && !supportOutPermission && (
            <ProFormSelect
              label="触发条件"
              width="md"
              name="transferType"
              placeholder="请选择触发条件"
              initialValue="NONE"
              rules={[
                {
                  required: false,
                  message: '请选择触发条件',
                },
              ]}
              valueEnum={{ OVERDUE: '工单超时', NONE: '无' }}
            />
          )}

          <ProFormRadio.Group
            name="needAudit"
            label="是否需要审核"
            disabled={supportOutPermission}
            normalize={(e) => {
              form.setFieldValue('auditExecuteDuration', undefined);

              return e;
            }}
            rules={[
              {
                required: true,
                message: '请选择是否需要审核',
              },
            ]}
            options={[
              {
                label: '否',
                value: false,
              },
              {
                label: '是',
                value: true,
              },
            ]}
          />

          {needAudit && (
            <ProFormCascader
              label="审核时长 "
              name="auditExecuteDuration"
              width="sm"
              rules={[{ required: true, message: '请选择审核时长' }]}
              fieldProps={{
                allowClear: true,
                placeholder: '请选择审核时长',
                options: genDurationOptions(TaskRepeatType.MONTH),
              }}
            />
          )}
          {taskType === 'PATROL' && (
            <>
              {needAudit && !supportOutPermission && (
                <ProFormRadio.Group
                  name="adjustTaskExecuteTimeFlag"
                  labelCol={{ span: 5 }}
                  label="审核人是否可调整任务执行时长"
                  normalize={(e) => {
                    form.setFieldValue('auditAddExecuteDuration', undefined);

                    return e;
                  }}
                  rules={[
                    {
                      required: true,
                      message: '请选择',
                    },
                  ]}
                  options={[
                    {
                      label: '否',
                      value: false,
                    },
                    {
                      label: '是',
                      value: true,
                    },
                  ]}
                />
              )}

              {adjustTaskExecuteTimeFlag && (
                <ProFormCascader
                  label="任务执行时长可增加 "
                  name="auditAddExecuteDuration"
                  labelCol={{ span: 5 }}
                  width="sm"
                  rules={[{ required: true, message: '请选择任务执行时长可增加' }]}
                  fieldProps={{
                    allowClear: true,
                    placeholder: '请选择任务执行时长可增加',
                    options: genDurationOptions(TaskRepeatType.MONTH),
                  }}
                />
              )}
            </>
          )}

          {needAudit && (
            <ProForm.Item
              name="auditRoleIds"
              label="转派审核角色"
              rules={[
                {
                  required: true,
                  message: '请选择转派审核角色',
                },
              ]}
            >
              <RoleCascader style={{ width: 400 }} multiple options={service?.roleOptions || []} />
            </ProForm.Item>
          )}

          {
            <ProFormList
              name="processorRoleIds"
              label=""
              copyIconProps={false}
              initialValue={isShowProcessor ? [{ roles: [] }] : [{ roles: [] }, { roles: [] }, { roles: [] }]}
              max={isShowProcessor ? 1 : 3}
              alwaysShowItemLabel={true}
              creatorButtonProps={false}
              deleteIconProps={false}
            >
              {(_, index) => {
                if (isShowProcessor && index > 0) {
                  return null;
                }

                return (
                  <div>
                    <ProForm.Item
                      labelCol={{ offset: 2 }}
                      label={`转派处理角色${index + 1}`}
                      rules={[
                        {
                          required: index === 0,
                          message: '请选择转派处理角色1',
                        },
                      ]}
                      name="roles"
                    >
                      <RoleCascader multiple style={{ width: 400 }} options={service?.roleOptions || []} />
                    </ProForm.Item>
                  </div>
                );
              }}
            </ProFormList>
          }
          {taskType !== 'REVIEW' && !supportOutPermission ? (
            <>
              <ProFormRadio.Group
                name="confirmFlag"
                label="是否需要确认"
                rules={[
                  {
                    required: true,
                    message: '请选择是否需要确认',
                  },
                ]}
                options={[
                  {
                    label: '否',
                    value: false,
                  },
                  {
                    label: '是',
                    value: true,
                  },
                ]}
              />
              <ProFormDependency name={['taskSubType']}>
                {({ taskSubType }) => {
                  return (
                    ![StrategyPatrolType.VIDEO, StrategyPatrolType.FOOD_SAFETY_VIDEO].includes(taskSubType) && (
                      <ProFormRadio.Group
                        name="supportChooseTransfer"
                        label="是否支持选择转派人"
                        labelCol={{ span: 3 }}
                        rules={[
                          {
                            required: true,
                            message: '请选择',
                          },
                        ]}
                        options={[
                          {
                            label: '否',
                            value: false,
                          },
                          {
                            label: '是',
                            value: true,
                          },
                        ]}
                      />
                    )
                  );
                }}
              </ProFormDependency>
            </>
          ) : null}
        </ProForm>
      </ProCard>
    </>
  );
};

export default TransferDetail;
