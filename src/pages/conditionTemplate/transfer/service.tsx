import useServiceReducer from '@/hooks/use-service-reducer';
import HttpAop from '@/http/aop';
import { getUserInfoList } from '@/http/apis/center-control';
import { getRoleClassifyList } from '@/http/apis/role';
import {
  getReferenceQuantityList,
  getTransferTemplateDetail,
  getTransferTemplateList,
  saveTransferTemplate,
  updateTransferTemplate,
  updateTransferTemplateStatus,
} from '@/http/apis/template';
import { formatRoleToOptions, formatUserListToRoleOptions } from '@/utils/format';

export enum RequestName {
  GetTransferTemplateList,
  GetTransferTemplateDetail,
  GetUserInfoList,
  UpdateTransferTemplate,
  SaveTransferTemplate,
  GetRoleCascaderData,
  UpdateTransferTemplateStatus,
  GetReferenceQuantity,
}

export const initState: any = {};

const Service = () => {
  const [service, executeRequest, dispatch] = useServiceReducer(initState, {
    [RequestName.GetTransferTemplateList]: {
      request: getTransferTemplateList,
    },
    [RequestName.GetTransferTemplateDetail]: {
      request: getTransferTemplateDetail,
    },
    [RequestName.UpdateTransferTemplate]: {
      request: updateTransferTemplate,
    },
    [RequestName.SaveTransferTemplate]: {
      request: saveTransferTemplate,
    },
    [RequestName.GetUserInfoList]: {
      beforeRequest: (dispatch) => dispatch({ personLoading: true }),
      request: HttpAop(getUserInfoList, {
        after: [formatUserListToRoleOptions],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ personOptions: data, personLoading: false });
      },
    },

    [RequestName.GetRoleCascaderData]: {
      request: HttpAop(getRoleClassifyList, {
        after: [formatRoleToOptions],
      }),
      afterRequest(data, dispatch) {
        dispatch({ roleOptions: data });
      },
    },
    [RequestName.UpdateTransferTemplateStatus]: {
      request: updateTransferTemplateStatus,
    },
    [RequestName.GetReferenceQuantity]: {
      request: getReferenceQuantityList,
    },
  });

  return [service, executeRequest, dispatch];
};

export default Service;
