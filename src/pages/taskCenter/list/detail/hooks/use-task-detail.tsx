import { useRef } from 'react';
import { Modal, Timeline } from 'antd';
import dayjs from 'dayjs';
import MediaCard from '@/components/MediaCard';
import { StrategyOperationActionCN } from '@/constants/strategy';

const ActionCN: Record<string, string> = {
  CANCELED: '作废任务',
  COMPLETED: '完成任务',
  EXPIRED: '逾期',
  CREATED: '创建',
  SUBMITTED: '提交',
  REVOCATION: '撤回',
  REVIEWS: '点评',
  REVOCATION_REVIEWS: '撤回点评',
  CONFIRMED: '确认',
  REVOCATION_WORKSHEET: '自检检查表撤回',
  FEED_BACK: '提交反馈',
  REJECTED: '驳回',
  AUDITED: '审核通过',
  CONFIRM_ARRIVE: '转办确认到店',
  TRANSFER_REJECT: '转办拒绝',
  TRANSFER_CANCEL: '转办取消',
  TRANSFER_APPROVED: '转办审核通过',
  TRANSFER_OVERDUE: '工单超时流转',
};

const useTaskDetailModal = () => {
  const modalRef: any = useRef();

  const showModal = ({ detail }) => {
    return (modalRef.current = Modal.info({
      title: '操作记录',
      closable: true,
      footer: null,
      destroyOnClose: true,
      icon: null,
      content: (
        <>
          <Timeline className="mt-4">
            {detail?.processes?.map(({ createTime, operatorName, operateAction, remark, imageResources }) => {
              return (
                <Timeline.Item key={createTime}>
                  <div>
                    <span>{operatorName}</span> 于
                    <span>{createTime && dayjs(createTime).format('YYYY-MM-DD HH:mm:ss')}</span>
                    <span> {StrategyOperationActionCN?.[operateAction]}</span>
                  </div>

                  {(remark || imageResources?.length) && (
                    <div className="rounded-lg bg-[#F7F8FA] p-2">
                      {/* CTODO: */}
                      {remark && <span className="text-sm leading-[22px] text-[#4E5969] break-all">{remark}</span>}
                      {!!imageResources?.length && (
                        <div className="flex flex-wrap gap-2 mt-2">
                          {imageResources?.map((item) => (
                            <MediaCard file={item} key={item.id} fileList={imageResources} width={60} height={60} />
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </Timeline.Item>
              );
            })}
          </Timeline>
        </>
      ),
      onOk: async () => {},
      onCancel: () => {},
    }));
  };

  return { showModal };
};

export default useTaskDetailModal;
