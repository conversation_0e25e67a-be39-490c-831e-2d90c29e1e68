import { createContext, useState } from 'react';
import { Empty, Tabs } from 'antd';
import { Detail } from './components/Detail';
import MapRoute, { AuditRoute } from './components/MapRoute';
import { Plan } from './components/Plan';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { usePermission } from '@/hooks/usePermission';

export const MapRouteContext = createContext<
  | { openMapDrawer: (val: AuditRoute) => void; changeTabAuditSchedulingTab: (key: auditSchedulingTabsEnum) => void }
  | undefined
>(undefined);

export enum auditSchedulingTabsEnum {
  任务计划 = '1',
  任务详情排班 = '2',
}

export default function AuditSchedulingManageIndex() {
  const hasPermission = usePermission();
  const [urlParams, setUrlParams] = useQuerySearchParams();

  const tabs = [
    {
      key: auditSchedulingTabsEnum.任务计划,
      label: '任务计划',
      children: <Plan />,
    },
    {
      key: auditSchedulingTabsEnum.任务详情排班,
      label: '任务详情排班',
      children: <Detail />,
      forceRender: true,
    },
  ];

  const [activeKey, setActiveKey] = useState(urlParams?.activeTab || tabs[0]?.key);

  const [mapState, setMapState] = useState<{
    visible: boolean;
    currentData?: AuditRoute;
  }>({ visible: false });

  if (!tabs.length) {
    return <Empty description="暂无权限" />;
  }

  const openMapDrawer = (val: AuditRoute) => {
    setMapState({
      visible: true,
      currentData: val,
    });
  };

  const changeTabAuditSchedulingTab = (key: auditSchedulingTabsEnum) => {
    setActiveKey(key);
    setUrlParams({ ...urlParams, activeTab: key });
  };

  return (
    <MapRouteContext.Provider value={{ openMapDrawer, changeTabAuditSchedulingTab }}>
      <Tabs items={tabs} activeKey={activeKey} onChange={changeTabAuditSchedulingTab} />
      <MapRoute
        open={mapState?.visible}
        onClose={() => {
          setMapState({
            visible: false,
          });
        }}
        data={mapState?.currentData}
      />
    </MapRouteContext.Provider>
  );
}
