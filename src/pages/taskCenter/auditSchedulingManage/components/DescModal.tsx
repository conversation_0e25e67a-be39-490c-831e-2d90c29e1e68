import { ProTable } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { PlanStatusEnumCN } from '../const';
import { getExecutionDetails } from '@/http/apis/task-center';
import createModal from '@/utils/antd/createModal';

interface DescModalProps {
  batchId: string;
}

export const DescModal = createModal<{}, DescModalProps>(
  (_p, setVisible, payload) => {
    const { batchId } = payload?.data || {};

    const { data, loading } = useRequest(() => getExecutionDetails(batchId), {
      refreshDeps: [batchId],
      ready: !!batchId && !!_p.visible,
    }) as any;

    const columns = [
      {
        title: '计划稽核门店数',
        dataIndex: 'plannedAuditShopCount',
      },
      {
        title: '计划状态',
        dataIndex: 'routeBatchStatus',
        valueEnum: PlanStatusEnumCN,
        width: 120,
      },
      {
        title: '进行中门店数',
        dataIndex: 'taskRunningCount',
      },
      {
        title: '已稽核门店数',
        dataIndex: 'completedCount',
      },
      {
        title: '逾期执行门店数',
        dataIndex: 'expiredRunningCount',
      },
      {
        title: '已过期门店数',
        dataIndex: 'expiredCount',
      },
      {
        title: '实际执行时长',
        dataIndex: 'actualDuration',
        render: (_, record) => {
          return record?.actualDuration ? `${record?.actualDuration}小时` : '-';
        },
      },
    ];

    return [
      {
        footer: null,
      },
      <ProTable
        loading={loading}
        columns={columns}
        search={false}
        options={false}
        dataSource={data ? [data] : []}
        pagination={false}
      />,
    ];
  },
  {
    title: '执行详情',
    width: 856,
  },
);
