import { useCallback, useMemo, useState } from 'react';
import { CloseCircleFilled, PlusOutlined } from '@ant-design/icons';
import {
  Key,
  ProForm,
  ProFormDateTimeRangePicker,
  ProFormDependency,
  ProFormSelect,
  ProFormUploadDragger,
} from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { message, Spin } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import MediaCard from '@/components/MediaCard';
import PictureInput from '@/components/picture-input';
import { useOSSClient } from '@/hooks/use-oss-client';
import { getUserInfoList } from '@/http/apis/center-control';
import { getAuditEnableList, modifyAuditPlanData, modifyAuditRouteData } from '@/http/apis/task-center';
import createModal from '@/utils/antd/createModal';
import { formatDateToUTC } from '@/utils/date';
import { formatUserListToRoleOptions } from '@/utils/format';

interface ModifyBatchModalFormProps {
  isInfoModal?: boolean;
  ids?: Key[];
  onSuccess?: () => void;
}

export const ModifyBatchModalForm = createModal<{}, ModifyBatchModalFormProps>(
  (_p, setVisible, payload) => {
    const { isInfoModal, ids, onSuccess } = payload?.data || {};

    const [form] = ProForm.useForm();

    const [loading, setLoading] = useState(false);
    const { uploadFile } = useCallback(useOSSClient, [])('PATROL');

    const { data: userOptions } = useRequest(getUserInfoList, {});
    const formatUserOptions = useMemo(() => {
      return formatUserListToRoleOptions(userOptions, true);
    }, [userOptions]);

    const { data: OptionLists } = useRequest(getAuditEnableList, {}) as any;

    const formatOptionLists = useMemo(() => {
      return (
        OptionLists?.map((o) => ({
          label: o?.reasonName,
          value: o?.id,
        })) || []
      );
    }, [OptionLists]);

    const { runAsync, loading: submitLoading } = useRequest(
      async (params: any) => {
        const { ids, ...rest } = params || {};
        const results: any[] = [];
        const fuc = isInfoModal ? modifyAuditRouteData : modifyAuditPlanData;

        for (const id of ids) {
          try {
            const res = await fuc?.({ ...rest, [isInfoModal ? 'id' : 'batchId']: id });

            if (res?.success) {
              results.push({ status: 'fulfilled', value: res });
            } else {
              results.push({ status: 'rejected', error: res });
            }
          } catch (error) {
            results.push({ status: 'rejected', error });
          }
        }

        return results;
      },
      {
        manual: true,
        onSuccess: (res) => {
          const resultStatus = res?.reduce(
            (acc, result) => {
              if (result.status === 'fulfilled') {
                acc.success++;
              } else {
                acc.failure++;
              }

              return acc;
            },
            { success: 0, failure: 0 },
          );

          message.success(`成功${resultStatus.success}条，失败${resultStatus.failure}条`);

          onSuccess?.();
          setVisible(false);
          form.resetFields();
        },
      },
    );

    return [
      {
        confirmLoading: submitLoading,
        onOk: () => {
          form.submit();
        },
        onCancel: () => {
          setVisible(false);
          form.resetFields();
        },
      },
      <ProForm
        form={form}
        onFinish={async (values) => {
          if (loading) {
            message.error('文件正在上传!');

            return;
          }

          const { itemImages, ...rest } = values;

          const payload = {
            ids,
            ...rest,
            images: itemImages?.map((item) => {
              return item?.response?.id;
            }),
          };

          await runAsync(payload);
        }}
        submitter={false}
      >
        <ProFormSelect
          label="执行人"
          name={'processNewUserId'}
          rules={[{ required: true, message: '请选择执行执行人' }]}
          options={formatUserOptions || []}
          fieldProps={{
            showSearch: true,
          }}
        />
        {isInfoModal && (
          <ProFormDateTimeRangePicker
            name="datetimeRange"
            label="执行时间"
            rules={[{ required: true, message: '请选择执行时间' }]}
            transform={([start, end]) => ({
              taskNewBeginTime: formatDateToUTC(dayjs(start)),
              taskNewEndTime: formatDateToUTC(dayjs(end)),
            })}
            fieldProps={{
              disabledDate: (current) => {
                // 禁用小于今天的日期
                if (current && current < dayjs().startOf('day')) {
                  return true;
                }

                return false;
              },

              format: 'YYYY-MM-DD HH:mm',
              showTime: {
                defaultValue: [dayjs('00:00', 'HH:mm'), dayjs('23:59', 'HH:mm')],
              },
            }}
          />
        )}
        <ProFormSelect
          label="修改原因"
          name={'remark'}
          rules={[{ required: true, message: '请选择修改原因' }]}
          options={formatOptionLists || []}
          fieldProps={{
            showSearch: true,
            labelInValue: true,
          }}
          transform={({ label }) => {
            return { remark: label };
          }}
        />
        <ProForm.Item label="辅助材料" name="itemImages" rules={[{ required: true, message: '请添加辅助材料' }]}>
          <PictureInput
            onChange={async (file) => {
              try {
                const response = await uploadFile(file, true);

                const itemImages: any = form.getFieldValue('itemImages') || [];

                form.setFieldValue(
                  'itemImages',
                  itemImages?.concat({
                    response,
                    status: 'done',
                    type: response?.contentType,
                    thumbUrl: response?.snapshotUrl,
                    uid: response?.id,
                  }),
                );
              } catch (e) {
                message.error(e);
              }
            }}
          />
          <ProFormUploadDragger
            name="itemImages"
            noStyle
            accept=".jpg,.png,.mp4,.jpeg"
            title={'点击或将文件拖到这里上传'}
            description={'支持.jpg/.png/.mp4/.jpeg格式，视频大小不得超过20M'}
            icon={<PlusOutlined />}
            max={9}
            fieldProps={{
              showUploadList: false,

              customRequest: async (e: any) => {
                try {
                  if (!e?.file?.type?.includes('video') && !e?.file?.type?.includes('image')) {
                    message.error('请上传图片或视频');

                    const itemImages = form.getFieldValue('itemImages');
                    const cloneItemImages = cloneDeep(itemImages);

                    cloneItemImages.pop();

                    form.setFieldValue('itemImages', cloneItemImages);

                    return;
                  }

                  form?.setFieldValue('loading', true);
                  setLoading(true);

                  const response = await uploadFile(e.file, true, null, {});

                  e.onSuccess(response);
                  form?.setFieldValue('loading', false);
                  setLoading(false);
                } catch (error) {
                  e.onError();

                  const itemImages = form.getFieldValue('itemImages');

                  const cloneItemImages = cloneDeep(itemImages);

                  cloneItemImages.pop();

                  form?.setFieldValue('itemImages', cloneItemImages);
                  form?.setFieldValue('loading', false);
                  setLoading(false);
                } finally {
                }
              },
            }}
          />
          <ProFormDependency name={['itemImages', 'loading']}>
            {({ itemImages, loading }) => {
              const images = itemImages?.filter(({ response }) => response).map(({ response }) => response);

              return (
                <div className="flex flex-wrap gap-2">
                  {itemImages?.map(({ response: item }, index) => {
                    return item ? (
                      <div className="relative inline-flex mt-2.5">
                        <CloseCircleFilled
                          className="absolute top-[-8px] right-[-8px] z-10  text-[#ff4d4f]"
                          style={{ fontSize: '16px' }}
                          onClick={() => {
                            form.setFieldValue(
                              'itemImages',
                              itemImages?.filter((_, i) => i !== index),
                            );
                          }}
                        />
                        <MediaCard file={item} key={item.id} fileList={images} width={80} height={80} />
                      </div>
                    ) : undefined;
                  })}
                  {loading && (
                    <Spin tip="上传中">
                      <div className="size-20" />
                    </Spin>
                  )}
                </div>
              );
            }}
          </ProFormDependency>
        </ProForm.Item>
      </ProForm>,
    ];
  },
  {
    title: '修改执行人',
    width: 528,
    destroyOnClose: true,
  },
);
