import { useContext, useState } from 'react';
import { EnvironmentOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { MapRouteContext } from '..';
import Service, { RequestName } from '../service';

interface CheckRouteButtonProps {
  batchId?: string;
}

export const CheckRouteButton = ({ batchId }: CheckRouteButtonProps) => {
  const { openMapDrawer } = useContext(MapRouteContext);

  const [loading, setLoading] = useState(false);

  const [_, executeRequest] = Service();

  return (
    <Button
      type="link"
      loading={loading}
      onClick={() => {
        if (batchId && openMapDrawer) {
          setLoading(true);

          executeRequest(RequestName.GetAuditRouteByBatchId, batchId)
            .then((res) => {
              openMapDrawer(res);
            })
            .finally(() => {
              setLoading(false);
            });
        }
      }}
    >
      <div className="flex flex-row items-center gap-1">
        <EnvironmentOutlined className="text-[#1577FF]" />
        <span className="text-[#1577FF]">查看路径</span>
      </div>
    </Button>
  );
};
