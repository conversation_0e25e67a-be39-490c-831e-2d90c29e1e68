import AMapLoader from '@amap/amap-jsapi-loader';
import end from '@/assets/images/end.svg';
import MapAuditIcon from '@/assets/images/map-audited.svg';
import MapNotAuditIcon from '@/assets/images/map-no-audited.svg';
import start from '@/assets/images/start.svg';

class MapRouteLine {
  private map: any = null;
  private AMap: any = null;
  // 当前路径规划
  private currentDriving: any = null;
  // 时间
  private time: string = '';
  // 距离
  private distance: string = '';
  // 原始定位数据
  private position: any = null;

  constructor(position) {
    // @ts-ignore
    window._AMapSecurityConfig = {
      securityJsCode: '180d07795656a13eaa0059339e4280be',
    };
    this.position = position;
  }

  /**
   * 初始化地图
   */
  async initMap(containerId: string) {
    try {
      this.AMap = await AMapLoader.load({
        key: '3691480a128250c7c159e46bea3db8a1',
        version: '2.0',
        plugins: [
          'AMap.ToolBar', // 缩放按钮
          'AMap.Scale', // 比例尺
          'AMap.Driving', // 驾车
        ],
      });

      this.map = new this.AMap.Map(containerId, {
        resizeEnable: true,
        zoom: 13,
        viewMode: '3D',
        center: [105.602725, 37.076636],
      });

      this.map.addControl(new this.AMap.ToolBar());
      this.map.addControl(new this.AMap.Scale());
    } catch (error) {
      console.error('地图初始化失败:', error);

      throw error;
    }
  }

  /**
   * 路径规划
   */
  async routePlanning(start: number[], end: number[], waypoints: number[][]) {
    const drivingOption = {
      policy: this.AMap.DrivingPolicy.LEAST_TIME, // 其它policy参数请参考 https://lbs.amap.com/api/javascript-api/reference/route-search#m_DrivingPolicy
    };

    this.currentDriving = new this.AMap.Driving(drivingOption);

    this.currentDriving.search(start, end, { waypoints }, (status, result) => {
      if (status === 'complete') {
        if (result.routes && result.routes.length) {
          // 绘制第一条路线，也可以按需求绘制其它几条路线
          this.customRouteLine(result.routes[0], waypoints);
          this.distance = (result.routes[0].distance / 1000).toFixed(1); // m -> km
          this.time = (result.routes[0].time / 60).toFixed(); // second -> min
        }
      }
    });
  }

  parseRouteToPath(route) {
    const path = [];

    for (let i = 0, l = route.steps.length; i < l; i++) {
      const step = route.steps[i];

      for (let j = 0, n = step.path.length; j < n; j++) {
        path.push(step.path[j]);
      }
    }

    return path;
  }

  /**
   * 自定义绘画路线
   */
  async customRouteLine(route: any, waypoints: any) {
    const path = this.parseRouteToPath(route);

    // start status
    const startStatus = this.position[0].taskStatus === 'COMPLETED';
    const endStatus = this.position[this.position.length - 1].taskStatus === 'COMPLETED';

    const startMarker = new this.AMap.Marker({
      position: path[0],
      icon: new this.AMap.Icon({
        size: new this.AMap.Size(140, 30),
        image: start,
        imageSize: new this.AMap.Size(24, 36),
      }),
      offset: new this.AMap.Pixel(-70, -90),
      map: this.map,
      content: `<div class="flex flex-col items-center">
        <div style=" text-shadow: 
        -1px -1px 0 white,
        1px -1px 0 white,
        -1px 1px 0 white,
        1px 1px 0 white;" class="text-sm font-medium text-[#1D2129]">${this.position[0].shopName}</div>
        <img src="${start}" alt="start" style="height:48px;width:32px;object-fit: contain;" />
        <img src="${startStatus ? MapAuditIcon : MapNotAuditIcon}" alt="start" style="height:48px;width:32px;" />
      </div>`,
    });

    startMarker.on('click', () => {
      console.log('startMarker');
    });

    // TODO：途径节点
    waypoints.map((item) => {
      const activeItem = this.position.find((p) => p.longitude === item[0] && p.latitude === item[1]);

      return new this.AMap.Marker({
        position: new this.AMap.LngLat(activeItem.longitude, activeItem.latitude),
        // 将一张图片的地址设置为 icon
        icon: new this.AMap.Icon({
          size: new this.AMap.Size(140, 30),
          imageSize: new this.AMap.Size(24, 32),
        }),
        // 设置了 icon 以后，设置 icon 的偏移量，以 icon 的 [center bottom] 为原点
        offset: new this.AMap.Pixel(-70, -32),
        map: this.map,
        content: `<div class="flex flex-col items-center">
          <div style=" text-shadow:
        -1px -1px 0 white,
        1px -1px 0 white,
        -1px 1px 0 white,
        1px 1px 0 white;" class="text-xs font-medium text-[#1D2129]">${activeItem.shopName}</div>
          <img src="${activeItem.taskStatus === 'COMPLETED' ? MapAuditIcon : MapNotAuditIcon}" alt="start" style="height:32px;width:24px" />
        </div>`,
      });
    });

    const endMarker = new this.AMap.Marker({
      position: path[path.length - 1],
      icon: new this.AMap.Icon({
        size: new this.AMap.Size(140, 30),
        image: end,
        imageSize: new this.AMap.Size(72, 90),
      }),
      offset: new this.AMap.Pixel(-70, -43),
      map: this.map,
      content: `<div class="flex flex-col items-center">
        <div style=" text-shadow: 
        -1px -1px 0 white,
        1px -1px 0 white,
        -1px 1px 0 white,
        1px 1px 0 white;" class="text-sm font-medium text-[#1D2129]">${this.position[this.position.length - 1].shopName}</div>
        <img src="${end}" alt="start" style="height:48px;width:32px"/>
        <img src="${endStatus ? MapAuditIcon : MapNotAuditIcon}" alt="audit" style="height:48px;width:32px" />
      </div>`,
    });

    const routeLine = new this.AMap.Polyline({
      path,
      bubble: true,
      isOutline: true,
      outlineColor: '#23C343',
      borderWeight: 1,
      strokeWeight: 6,
      strokeOpacity: 0.5,
      strokeColor: '#7BE188',
      lineJoin: 'round',
      showDir: true,
    });

    routeLine.setMap(this.map);

    // 调整视野达到最佳显示区域
    this.map.setFitView([startMarker, endMarker, routeLine]);
  }

  /**
   * 获取地图实例
   */
  getMap() {
    return this.map;
  }

  /**
   * 获取AMap实例
   */
  getAMap() {
    return this.AMap;
  }

  /**
   * 获取距离
   */
  getDistance() {
    return this.distance;
  }

  /**
   * 获取耗时
   */
  getTime() {
    return this.time;
  }

  /**
   * 销毁地图
   */
  destroy() {
    if (this.map) {
      this.map.destroy();
      this.map = null;
    }
  }
}

export default MapRouteLine;
