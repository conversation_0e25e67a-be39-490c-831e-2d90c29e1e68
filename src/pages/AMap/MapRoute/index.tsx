import { useEffect, useMemo, useRef } from 'react';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { decrypt } from '@src/utils/utils';
import MapRouteLine from './RouteLine';

interface FoodSafetyNormalRoutShopInfoDTO {
  /**
   * 稽核时间 单位：分钟
   */
  auditTime?: number | null;
  /**
   * 纬度
   */
  latitude: number;
  /**
   * 经度
   */
  longitude: number;
  /**
   * 门店地址
   */
  shopAddress?: null | string;
  /**
   * 门店ID
   */
  shopId?: null | string;
  /**
   * 门店名称
   */
  shopName?: null | string;
  /**
   * 排序
   */
  sort?: number | null;
  /**
   * 任务ID
   */
  taskId?: number | null;
  /**
   * 任务状态
   */
  taskStatus?: any;
  /**
   * 到达该门店时间 单位：分钟
   */
  travelTime?: number | null;
}

type shopInfoDTOS = FoodSafetyNormalRoutShopInfoDTO[] | null;

export default function MapRouteIndex() {
  const [searchParams] = useQuerySearchParams<{ shopInfo?: string }>();

  const mapInstance = useRef<MapRouteLine | null>(null);

  const shopInfo: shopInfoDTOS = useMemo(() => {
    try {
      if (!searchParams?.shopInfo) {
        return [];
      }
      const decryptedData = decrypt(searchParams.shopInfo);
      // 验证解密后的数据是否为数组
      if (!Array.isArray(decryptedData)) {
        console.warn('Decrypted data is not an array:', decryptedData);
        return [];
      }
      return decryptedData;
    } catch (error) {
      return [];
    }
  }, [searchParams?.shopInfo]);

  const position = useMemo(() => {
    if (shopInfo.length) {
      const start = [shopInfo[0].longitude, shopInfo[0].latitude];
      const end = [shopInfo[shopInfo.length - 1].longitude, shopInfo[shopInfo.length - 1].latitude];
      const waypoints = shopInfo.slice(1, shopInfo.length - 1).map((item) => [item.longitude, item.latitude]);

      return {
        start,
        end,
        waypoints,
      };
    }
  }, [shopInfo]);

  useEffect(() => {
    const initializeMap = async () => {
      try {
        mapInstance.current = new MapRouteLine(shopInfo);

        await mapInstance.current.initMap('container');

        // 现在可以安全地调用路径规划了
        position && (await mapInstance.current.routePlanning(position.start, position.end, position.waypoints));
      } catch (error) {
        console.error('地图初始化失败:', error);
      }
    };
    if (shopInfo.length) {
      initializeMap();
    }

    const messageHandler = (event: any) => {
      const messageData = JSON.parse(event.data)?.data;
      if (messageData?.type === 'amap') {
        mapInstance.current?.getMap().setCenter(messageData?.location);
        // 放大
        mapInstance.current?.getMap().setZoom(15);
      }
    };

    document.addEventListener('message', messageHandler);
    window.addEventListener('message', messageHandler);
    return () => {
      document.removeEventListener('message', messageHandler);
      window.removeEventListener('message', messageHandler);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!shopInfo?.length) {
    return '参数异常, 无法初始化地图';
  }

  return <div id="container" className="w-full h-full" />;
}
