import { useEffect, useRef, useState } from 'react';
import { ActionType, ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import { Button, message, Popconfirm, Space } from 'antd';
import CreateOrEditReasonModal, { EditReasonInitialValues } from './createOrEditReasonModal';
import Service, { RequestName } from './service';
import { getColumns, getCommonConfig, onReset } from '@/components/pro-table-config';
import useQuerySearchParams from '@/hooks/use-query-search-params';

const statusMap = {
  false: {
    text: '禁用',
    status: 'processing', // 红色
  },
  true: {
    text: '启用',
    status: 'Success', // 绿色
  },
};

interface ReasonMaintenanceProps {
  /** 是否为稽核原因 */
  isAudit?: boolean;
}

export const ReasonMaintenance = ({ isAudit }: ReasonMaintenanceProps) => {
  const [_, executeRequest] = Service();
  const [form] = ProForm.useForm();
  const actionRef = useRef<ActionType>();
  const [createOrEditVisible, setCreateOrEditVisible] = useState(false);
  const [editInitialValues, setEditInitialValues] = useState<EditReasonInitialValues>();
  const [searchParams, setSearchParams] = useQuerySearchParams();

  const commonConfig = getCommonConfig({
    search: {
      form,
    },
  });

  const columns = getColumns({
    searchColumns: [
      {
        title: '原因名称',
        dataIndex: 'value',
        fieldProps: {
          maxLength: 40,
        },
      },
      {
        title: '状态',
        dataIndex: 'enabled',
        valueEnum: {
          false: '禁用',
          true: '启用',
        },
      },
    ],
    tableColumns: [
      {
        title: '原因名称',
        dataIndex: 'value',
        ellipsis: true,
      },
      {
        title: '状态',
        dataIndex: 'enabled',
        valueEnum: statusMap,
      },
      {
        title: '操作',
        dataIndex: 'opr',
        search: false,
        render: (_, record) => {
          const statusText = record?.enabled ? '禁用' : '启用';

          return (
            <Space>
              <a
                onClick={() => {
                  setCreateOrEditVisible(true);
                  setEditInitialValues(record);
                }}
              >
                编辑
              </a>
              <Popconfirm
                title={`确定要${statusText}该不合格原因吗？`}
                onConfirm={() => {
                  if (isAudit) {
                    return executeRequest(
                      record?.enabled
                        ? RequestName.DisableAuditUnqualifiedReason
                        : RequestName.EnableAuditUnqualifiedReason,
                      { id: record.id },
                    )
                      .then(() => record.id)
                      .then(() => {
                        actionRef?.current?.reload();
                        message.success(`${statusText}成功！`);
                      });
                  }

                  return executeRequest(RequestName.UpdataReasonStatus, { enabled: !record?.enabled, id: record.id })
                    .then(() => record.id)
                    .then(() => {
                      actionRef?.current?.reload();
                      message.success(`${statusText}成功！`);
                    });
                }}
              >
                <a>{statusText}</a>
              </Popconfirm>
            </Space>
          );
        },
      },
    ],
  });

  useEffect(() => {
    form.setFieldsValue({
      value: searchParams?.value,
      enabled: searchParams?.enabled,
    });
  }, []);

  return (
    <>
      <ProTable
        {...commonConfig}
        actionRef={actionRef}
        columns={columns}
        tableRender={(_d, _p, { table }) => {
          return (
            <ProCard
              extra={
                <Button
                  type="primary"
                  onClick={() => {
                    setCreateOrEditVisible(true);
                    setEditInitialValues(undefined);
                  }}
                >
                  新建
                </Button>
              }
            >
              {table}
            </ProCard>
          );
        }}
        onReset={() => onReset(form)}
        request={async ({ current, pageSize, ...rest }) => {
          const params = {
            ...(form.getFieldsValue() || {}),
            pageNo: current || 1,
            pageSize: pageSize || 20,
            ...rest,
          };

          setSearchParams(params);

          const res = await executeRequest(
            isAudit ? RequestName.GetAuditUnqualifiedReasonPage : RequestName.GetUnqualifiedReasonPage,
            params,
          );

          return {
            data: res?.data || [],
            success: true,
            total: res.total,
          };
        }}
      />

      <CreateOrEditReasonModal
        initialValues={editInitialValues}
        open={createOrEditVisible}
        onCancel={() => setCreateOrEditVisible(false)}
        onSuccess={() => {
          setCreateOrEditVisible(false);
          actionRef.current?.reload();
        }}
        isAudit={isAudit}
      />
    </>
  );
};
