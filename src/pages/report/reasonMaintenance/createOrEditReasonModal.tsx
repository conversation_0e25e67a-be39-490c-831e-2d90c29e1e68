import { useEffect } from 'react';
import { Form, Input, message, Modal, ModalProps } from 'antd';
import Service, { RequestName } from './service';

export interface EditReasonInitialValues {
  id?: number;
  reason?: string;
}

interface CreateOrEditReasonModalProps extends ModalProps {
  initialValues?: EditReasonInitialValues;
  onSuccess: () => void;
  isAudit?: boolean;
}

const CreateOrEditReasonModal: React.FC<CreateOrEditReasonModalProps> = ({
  open,
  initialValues,
  onSuccess,
  isAudit,
  ...props
}) => {
  const [form] = Form.useForm();
  const isCreate = !initialValues;
  const [_, executeRequest] = Service();

  const handleSave = async ({ ...values }: any) => {
    if (isAudit) {
      await executeRequest(
        isCreate ? RequestName.CreateAuditUnqualifiedReason : RequestName.EditAuditUnqualifiedReason,
        {
          // 编辑时，这里面包含 id
          id: initialValues?.id,
          reasonName: values?.value,
        },
      );
    } else {
      await executeRequest(isCreate ? RequestName.AddReason : RequestName.UpdataReasonContent, {
        // 编辑时，这里面包含 id
        id: initialValues?.id,
        data: values?.value,
        ...values,
      });
    }

    message.success(isCreate ? '创建成功' : '编辑成功');
    onSuccess();
  };

  useEffect(() => {
    if (open) {
      // 要配合 forceRender
      form.resetFields();
    }
  }, [open]);

  return (
    <Modal open={open} forceRender title={isCreate ? '新建不合格原因' : '编辑不合格原因'} onOk={form.submit} {...props}>
      <Form
        form={form}
        labelCol={{ span: 6 }}
        // 编辑 || 新建
        initialValues={initialValues}
        onFinish={handleSave}
      >
        <Form.Item
          label="不合格原因"
          name="value"
          rules={[
            { required: true, message: '请输入不合格原因' },
            { max: 40, message: '最多只能输入 40 个字符' },
          ]}
        >
          <Input placeholder="请输入不合格原因" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateOrEditReasonModal;
