import { useState } from 'react';
import { useRoute } from '@react-navigation/native';
import { WebView } from '@src/components/WebView';
import { webviewRefPlugin } from '@src/components/WebView/WebViewPlugin';
import { useSupervisorFoodSafetyNormalRouteBatchDataPageList } from '@src/http/service/tactics/supervisorTask';
import Empty from '@src/ui/components/Empty';
import { encrypt } from '@src/utils';
import { disposeResult } from '@tastien/rn-bridge/lib/native/mange/utils';
import dayjs from 'dayjs';
import { isNil } from 'lodash';
import { ScrollView, Text, View } from 'react-native';
import { MapInfo } from './components/MapInfo';
import { TimeLine } from './components/TimeLine';

const url = 'http://192.168.122.158:5174/#/amap/routeLine';

export default function OptimalPathIndex() {
  const { params } = useRoute() as { params: { batchId: string } };
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const { data: routeData } = useSupervisorFoodSafetyNormalRouteBatchDataPageList({
    queryKey: ['supervisorFoodSafetyNormalRouteBatchDataPageList', { params }],
    params,
    enabled: !!params?.batchId,
  });

  if (!routeData?.shopInfoDTOS?.length) {
    return <Empty text="暂无数据" />;
  }

  const handGoToway = ({ location, index }: { location: string[]; index: number }) => {
    setActiveIndex(index);
    webviewRefPlugin.getWebViewRef?.current?.postMessage(
      JSON.stringify(disposeResult(true, { type: 'amap', location })),
    );
  };

  return (
    <View className="flex-1 bg-[#F7F8FA]">
      <View className="bg-white">
        <View className="h-90">
          <WebView url={`${url}?shopInfo=${encodeURIComponent(encrypt(routeData?.shopInfoDTOS))}`} />
        </View>
        <MapInfo activeShopInfo={!isNil(activeIndex) ? routeData?.shopInfoDTOS[activeIndex!] : undefined} />
      </View>
      <ScrollView className="mt-3 flex flex-1 bg-white">
        <View className="p-3">
          <View className="flex-col gap-y-1 rounded-lg bg-[#F7F8FA] p-3">
            <View className="flex-row items-center justify-between">
              <View className="flex-row gap-x-1">
                <View className="rounded border-[0.5px] border-[#0E42D2] px-[4.5px]">
                  <Text className="text-display-xs text-[#0E42D2]">{routeData?.batchId}</Text>
                </View>
                <Text className="text-display-2sm font-medium text-[#1D2129]">
                  {/* CTODO: */}
                  已查门店：0/{routeData?.shopInfoDTOS?.length}
                </Text>
              </View>
              <Text className="text-display-sm text-[#0E42D2]">进行中</Text>
            </View>
            <View>
              <View className="flex-row items-center gap-x-2">
                <Text className="text-display-sm text-[#4E5969]">
                  途经点：{routeData?.shopInfoDTOS?.length - 2}个门店
                </Text>
                <View className="h-4 w-[0.5px] bg-[#C9CDD4]" />
                {/* CTODO:h5通知rn获取里程 */}
                <Text className="text-display-sm text-[#4E5969]">总行程：8.5公里</Text>
              </View>
              <Text className="text-display-sm text-[#4E5969]">
                执行时段：{dayjs(routeData?.beginTime).format('YYYY/MM/DD HH:mm')} -{' '}
                {dayjs(routeData?.endTime).format('YYYY/MM/DD HH:mm')}
              </Text>
              <Text className="text-display-sm text-[#4E5969]">预估共{routeData?.estimateExecuteTime || 0}天</Text>
            </View>
          </View>
          <View className="mt-4">
            <TimeLine shopInfoDTOS={routeData?.shopInfoDTOS} handGoToway={handGoToway} />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}
