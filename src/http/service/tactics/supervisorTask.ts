import { useIsFocused } from '@react-navigation/native';
import { SortEnum } from '@src/biz/supervisor/schedule/components/Sort';
import {
  InfiniteData,
  QueryKey,
  UndefinedInitialDataInfiniteOptions,
  UndefinedInitialDataOptions,
  useInfiniteQuery,
  useQueries,
  useQuery,
} from '@tanstack/react-query';
import http from '../../client';
import { ClientError } from '../../utils';
import { Items, TaskSubType, TaskType } from '../task-center';

export enum TodayTaskQueryTypeEnum {
  自检任务 = 'ONLY_SELF_TASK',
  巡检任务 = 'ONLY_PATROL_TASK',
  常规任务 = 'ONLY_ROUTINES_TASK',
  食安稽核任务 = 'ONLY_FOOD_SAFETY_TASK',
  消杀任务 = 'ONLY_DISINFECTION_TASK',
  其他 = 'OTHER',
}

type TSupervisorTodayTaskTacticsData = {
  pageNo: number;
  pageSize: number;
} & Partial<{
  orderType: SortEnum;
  firstQuery: boolean;
  queryType: TodayTaskQueryTypeEnum;
  keyByUserId: string;
  taskSubTypeList: ['SELF'];
}>;

interface TaskResponse {
  taskId: number;
  taskName: string;
  shopId: string;
  shopName: string;
  shopType: string;
  shopAddress: string;
  shopPhone: string;
  appStatisticTaskType: string;
}

interface ISupervisorTaskTacticsResponse {
  taskResponse: TaskResponse[];
}

/**
 * 获取督导今日任务
 * @param data
 * @returns
 */
function getSupervisorTodayTaskTactics(data: TSupervisorTodayTaskTacticsData) {
  return http.post<ISupervisorTaskTacticsResponse[]>('/tm-api/corp/app/task/today-list', data);
}

/**
 * 获取督导今日任务 hook
 * @param param0
 * @returns
 */
export function useInfiniteQuerySupervisorTodayTaskTactics({
  params: { pageSize = 10, ...restParams },
  queryKey,
  ...options
}: {
  params: Omit<TSupervisorTodayTaskTacticsData, 'pageNo' | 'pageSize'> & { pageSize?: number };
  queryKey: QueryKey;
} & Partial<
  Omit<
    UndefinedInitialDataInfiniteOptions<
      ISupervisorTaskTacticsResponse[],
      ClientError,
      InfiniteData<ISupervisorTaskTacticsResponse[], number>,
      QueryKey,
      number
    >,
    'queryKey' | 'queryFn' | 'getNextPageParam'
  >
>) {
  return useInfiniteQuery({
    queryKey,
    queryFn: async ({ pageParam }) =>
      await getSupervisorTodayTaskTactics({
        ...restParams,
        pageNo: pageParam,
        pageSize,
      }),
    initialPageParam: 1,
    getNextPageParam: (last, all) => {
      const hasNext = (last?.length || 0) >= pageSize;

      if (hasNext) {
        return all.length + 1;
      }

      return;
    },
    ...options,
  });
}

export enum TodoTaskQueryTypeEnum {
  自检任务 = 'SELF_TASK',
  巡检任务 = 'PATROL_TASK',
  食安稽核任务 = 'FOOD_SAFETY_TASK',
  消杀任务 = 'DISINFECTION_TASK',
  常规任务 = 'ROUTINES_TASK',
  诊断任务 = 'DIAGNOSTIC_TASK',
}

type TSupervisorTodoTaskTacticsData = {
  queryType: TodoTaskQueryTypeEnum;
} & Partial<{
  shopId: string;
  pageNo: number;
  pageSize: number;
  orderType: SortEnum;
}>;

/**
 * 获取督导待办任务
 * @param data
 * @returns
 */
function getSupervisorTodoTaskTactics(data: TSupervisorTodoTaskTacticsData) {
  return http.post<ISupervisorTaskTacticsResponse[]>('/tm-api/corp/app/task/todo-list', data);
}

/**
 * 获取督导待办任务 hook
 * @param param0
 * @returns
 */
export function useInfiniteQuerySupervisorTodoTaskTactics({
  params: { pageSize = 10, ...restParams },
  queryKey,
  ...options
}: {
  params: Omit<TSupervisorTodoTaskTacticsData, 'pageNo' | 'pageSize'> & { pageSize?: number };
  queryKey: QueryKey;
} & Partial<
  Omit<
    UndefinedInitialDataInfiniteOptions<
      ISupervisorTaskTacticsResponse[],
      ClientError,
      InfiniteData<ISupervisorTaskTacticsResponse[], number>,
      QueryKey,
      number
    >,
    'queryKey' | 'queryFn' | 'getNextPageParam'
  >
>) {
  return useInfiniteQuery({
    queryKey,
    queryFn: async ({ pageParam }) =>
      await getSupervisorTodoTaskTactics({
        ...restParams,
        pageNo: pageParam,
        pageSize,
      }),
    initialPageParam: 1,
    getNextPageParam: (last, all) => {
      const hasNext = (last?.length || 0) >= pageSize;

      if (hasNext) {
        return all.length + 1;
      }

      return;
    },
    ...options,
  });
}

interface ISupervisorTaskCountTacticsData {
  taskCountSum: number;
  reviewReportCount: number;
  patrolTaskCount: number;
}

/**
 * @description 获取督导今日任务数量 hook
 * @param param0
 * @returns
 */
export function useQuerySupervisorTodayTaskCountTactics({
  queryKey,
  ...options
}: { queryKey: QueryKey } & Partial<
  Omit<UndefinedInitialDataOptions<ISupervisorTaskCountTacticsData, ClientError>, 'queryKey' | 'queryFn'>
>) {
  return useQuery({
    queryKey,
    queryFn: async () => await http.post<ISupervisorTaskCountTacticsData>('/tm-api/corp/app/task/today-count'),
    ...options,
  });
}

/**
 * @description 获取督导待办任务数量 hook
 * @param param0
 * @returns
 */
export function useQuerySupervisorTodoTaskCountTactics({
  queryKey,
  ...options
}: { queryKey: QueryKey } & Partial<
  Omit<UndefinedInitialDataOptions<ISupervisorTaskCountTacticsData, ClientError>, 'queryKey' | 'queryFn'>
>) {
  return useQuery({
    queryKey,
    queryFn: async () => await http.post<ISupervisorTaskCountTacticsData>('/tm-api/corp/app/task/todo-count'),
    ...options,
  });
}

export const useSupervisorTask = () => {
  const isFocused = useIsFocused();

  return useQueries({
    queries: [
      {
        queryKey: ['supervisorTaskCountSum', isFocused],
        queryFn: () => http.post('/tm-api/corp/app/task/today-count'),
        enabled: isFocused,
      },
      {
        queryKey: ['querySupervisorTodayTaskCountTactics', isFocused],
        queryFn: () => http.post('/tm-api/corp/app/task/today-count'),
        enabled: isFocused,
      },
      {
        queryKey: ['querySupervisorTodoTaskCountTactics', isFocused],
        queryFn: () => http.post('/tm-api/corp/app/task/todo-count'),
        enabled: isFocused,
      },
    ],
  });
};

// 待办任务-稽核计划
export interface AppTodoTaskListAuditPlanResponse {
  /**
   * 任务类型
   */
  appStatisticTaskType?:
    | 'PATROL_TASK'
    | 'SELF_TASK'
    | 'FOOD_SAFETY_NORMAL_AUDIT_PLAN_TASK'
    | 'FOOD_SAFETY_NORMAL_AUDIT_PLAN_TRANSFER_TASK';
  /**
   * 批次id
   */
  batchId?: number;
  deletable?: boolean;
  /**
   * 结束时间
   */
  endTime?: string;
  /**
   * 执行截止时间
   */
  expiredTime?: string;
  id?: number;
  items?: Items[];
  /**
   * 是否比较门店
   */
  mustFlag?: boolean;
  /**
   * 线路id
   */
  routeId?: number;
  /**
   * 门店地址
   */
  shopAddress?: string;
  /**
   * 门店Id
   */
  shopId?: string;
  /**
   * 门店名称
   */
  shopName?: string;
  /**
   * 门店电话
   */
  shopPhone?: string;
  /**
   * 门店状态 1:准备中、2:营业中、3:停业中、4:闭店中
   */
  shopStatus?: number;
  /**
   * 门店类型
   */
  shopType?: string;
  /**
   * 执行开始时间
   */
  startTime?: string;
  /**
   * 任务id
   */
  taskId?: number;
  /**
   * 任务名称
   */
  taskName?: string;
  /**
   * 任务子类型
   */
  taskSubType?: TaskSubType;
  /**
   * 类型
   * 任务类型
   */
  taskType?: TaskType;
  [property: string]: any;
}
export function useSupervisorTodoAuditPlanTactics({
  queryKey,
  ...options
}: { queryKey: QueryKey } & Partial<
  Omit<UndefinedInitialDataOptions<AppTodoTaskListAuditPlanResponse[], ClientError>, 'queryKey' | 'queryFn'>
>) {
  return useQuery({
    queryKey,
    queryFn: async () =>
      await http.get<AppTodoTaskListAuditPlanResponse[]>('/tm-api/corp/app/task/todo-list/audit-plan'),
    ...options,
  });
}

export interface AuditRoute {
  /**
   * 批次ID
   */
  batchId?: null | string;
  /**
   * 批次开始时间
   */
  beginTime?: string;
  /**
   * 批次结束时间
   */
  endTime?: string;
  /**
   * 预估执行时长，单位分钟
   */
  estimateExecuteTime?: number | null;
  /**
   * 线路ID
   */
  routeId?: number | null;
  /**
   * 门店信息列表
   */
  shopInfoDTOS?: FoodSafetyNormalRoutShopInfoDTO[] | null;
}

export interface FoodSafetyNormalRoutShopInfoDTO {
  /**
   * 稽核时间 单位：分钟
   */
  auditTime?: number | null;
  /**
   * 纬度
   */
  latitude?: null | string;
  /**
   * 经度
   */
  longitude?: null | string;
  /**
   * 门店地址
   */
  shopAddress?: null | string;
  /**
   * 门店ID
   */
  shopId?: null | string;
  /**
   * 门店名称
   */
  shopName?: null | string;
  /**
   * 排序
   */
  sort?: number | null;
  /**
   * 任务ID
   */
  taskId?: number | null;
  /**
   * 任务状态
   */
  taskStatus?: any;
  /**
   * 到达该门店时间 单位：分钟
   */
  travelTime?: number | null;
}
// 根据批次id获取稽核路线
export function useSupervisorFoodSafetyNormalRouteBatchDataPageList({
  params,
  queryKey,
  ...options
}: { queryKey: QueryKey; params: { batchId: string } } & Partial<
  Omit<UndefinedInitialDataOptions<AuditRoute, ClientError>, 'queryKey' | 'queryFn'>
>) {
  return useQuery({
    queryKey,
    queryFn: async () =>
      await http.get<AuditRoute>(`/tm-api/corp/food-safety-normal-route/batch/${params?.batchId}/audit-route/`),
    ...options,
  });
}
