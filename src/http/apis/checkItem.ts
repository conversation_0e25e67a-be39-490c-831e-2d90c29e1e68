import { useRequest } from 'ahooks';
import { Options } from 'ahooks/lib/useRequest/src/types';
import { del, get, post, put } from '..';
import { URLRequestPrefix } from '../config';
import { ChecklistStatus } from '@/constants/checklist';

export type TCheckItemData = {
  pageNo: number;
  pageSize: number;
} & Partial<{
  name: string;
  typeId: number;
  status: string;
  orderBy: string;
}>;

export interface ICheckItemListItem {
  accentedTermTags: string[];
  createTime: string;
  createUserName: string;
  id: number;
  name: string;
  refNum: number;
  status: string;
  typeId: number;
  differentItem: boolean;
}

/** 获取关联检查项列表 */
export async function getCheckItemList(params: TCheckItemData) {
  return await get<PageRes<ICheckItemListItem>>(`${URLRequestPrefix.OM}/corp/association-check-item/page`, {
    params,
  });
}

export type ICheckItemDetail = Omit<ICheckItemListItem, 'createTime' | 'createUserName' | 'refNum'>;

/** 查询关联检查项 */
export async function queryCheckItemDetail(params: { id: number }) {
  return await get<ICheckItemDetail>(`${URLRequestPrefix.OM}/corp/association-check-item/${params.id}`);
}

interface INumberOfCitationListItem {
  createUserId: number;
  createUserName: string;
  createTime: string;
  hasPeek: boolean;
  id: number;
  sheetName: string;
  sheetStatus: ChecklistStatus;
  type: string;
  createUserInfo: {
    // 账号是否存在 false-表示已删除
    alive: boolean;
    userId: number;
    nickname: string;
    phone: string;
    open: boolean;
  };
}

/** 获取引用量列表 */
export async function getNumberOfCitationList(params: {
  associateCheckItemId: number;
  pageNo: number;
  pageSize: number;
}) {
  return (await get(`${URLRequestPrefix.OM}/corp/worksheet/ref/associate-check-item/detail`, {
    params,
  })) as unknown as PageRes<INumberOfCitationListItem>;
}

type CheckItemData = {
  typeId: number;
  name: string;
  accentedTermTags: string[];
};

/** 添加关联检查项 */
export async function addedCheckItem(data: CheckItemData) {
  return await post(`${URLRequestPrefix.OM}/corp/association-check-item/`, data);
}

/** 删除检查项类型 */
export async function deleteCheckItem(id: number) {
  return await del(`${URLRequestPrefix.OM}/corp/association-check-item/${id}`);
}

/** 修改检查项 */
export async function updateCheckItem(data: CheckItemData & { id: number }) {
  return await put(`${URLRequestPrefix.OM}/corp/association-check-item/`, data);
}

/** 复制检查项 */
export async function copyCheckItem(data: { id: number; copyName: string }) {
  return await post(`${URLRequestPrefix.OM}/corp/association-check-item/copy`, data);
}

/** 禁用关联检查项 */
export async function disableCheckItem(id: number) {
  return await put(`${URLRequestPrefix.OM}/corp/association-check-item/${id}/disable`);
}

/** 启用关联检查项 */
export async function enableCheckItem(id: number) {
  return await put(`${URLRequestPrefix.OM}/corp/association-check-item/${id}/enable`);
}

/** 分页查询差异项门店配置 */
export async function differentItemList(data: { associationItemId: number; pageNo: number; pageSize: number }) {
  return await post(`${URLRequestPrefix.OM}/corp/different-item-shop/page`, data);
}

/** 差异项门店配置操作日志 */
export async function getDifferentItemOprLog(params: { id: number }) {
  return await get(`${URLRequestPrefix.OM}/corp/different-item-shop/logs`, {
    params,
  });
}

/** 变更差异项门店配置 */
export async function updateDifferentItem(data: { id: number; itemType: string }) {
  return await post(`${URLRequestPrefix.OM}/corp/different-item-shop/update`, data);
}

interface ICheckItem {
  id: number;
  createTime: string;
  name: string;
}

/** 获取关联检查项类型列表 */
export async function getCheckItemTypeList(params: { pageNo: number; pageSize: number }) {
  return await get<PageRes<ICheckItem>>(`${URLRequestPrefix.OM}/corp/association-check-item/type/page`, {
    params,
  });
}

interface ICheckItemTypeListItem {
  id: number;
  name: string;
}

/** 获取所有关联检查项类型列表 */
async function getAllCheckItemTypeList() {
  return await get<ICheckItemTypeListItem[]>(`${URLRequestPrefix.OM}/corp/association-check-item/type/simple-list`);
}

/** 获取所有关联检查项类型列表 hook */
export function useAllCheckItemTypeList(options?: Options<ICheckItemTypeListItem[], []>) {
  return useRequest(
    async () => {
      const res = await getAllCheckItemTypeList();

      return (res || []) as unknown as ICheckItemTypeListItem[];
    },
    {
      cacheKey: 'allCheckItemTypeList',
      // staleTime: 1000 * 60 * 5,
      onError: (error) => {
        console.log('error :>> ', error);
      },
      ...options,
    },
  );
}

/** 添加关联检查项类型 */
export async function addedCheckItemType(data: { name: string }) {
  return await post(`${URLRequestPrefix.OM}/corp/association-check-item/type`, data);
}

/** 删除关联检查项类型 */
export async function deleteCheckItemType(id: number) {
  return await del(`${URLRequestPrefix.OM}/corp/association-check-item/type/${id}`);
}

/** 修改关联检查项类型 */
export async function updateCheckItemType(data: { id: number; name: string }) {
  return await put(`${URLRequestPrefix.OM}/corp/association-check-item/type/rename`, data);
}

export interface ICheckItemGroupTypeListItem {
  id: number;
  name: string;
  items: Omit<ICheckItemGroupTypeListItem, 'items'>[];
}

type TCheckItemGroupTypeData = {
  /** 状态 */
  status: 'ENABLE' | 'DISABLE';
  /** 是否过滤子项为空数据 */
  filterTypeEmptyData: boolean;
};

/** 获取关联检查项组类型 */
export async function getCheckItemGroupType(params: TCheckItemGroupTypeData) {
  return await get<ICheckItemGroupTypeListItem[]>(
    `${URLRequestPrefix.OM}/corp/association-check-item/simple-list/group-by-type`,
    { params },
  );
}

/** 关联检查项组类型 hook */
export function useCheckItemGroupByType(
  params?: TCheckItemGroupTypeData,
  options?: Options<ICheckItemGroupTypeListItem[], []>,
) {
  return useRequest(
    async () => {
      const res = await getCheckItemGroupType({
        status: params?.status || 'ENABLE',
        filterTypeEmptyData: params?.filterTypeEmptyData || true,
      });

      return (res || []) as unknown as ICheckItemGroupTypeListItem[];
    },
    {
      cacheKey: 'checkItemGroupType',
      // staleTime: 1000 * 60 * 5,
      onError: (error) => {
        console.log('error :>> ', error);
      },
      ...options,
    },
  );
}
/** 获取所有启用的不合格原因*/
export async function getUnqualifiedReasonList(params: {}) {
  return await get<{ enabled: boolean; id: number; value: string }[]>(
    `${URLRequestPrefix.OM}/corp/unqualified-reasons/enabled`,
    {
      params,
    },
  );
}
/** 获取所有启用的不合格原因分页*/
export async function getUnqualifiedReasonPage(params: {}) {
  return await get<PageRes<{ enabled: boolean; id: number; value: string }[]>>(
    `${URLRequestPrefix.OM}/corp/unqualified-reasons/page`,
    {
      params,
    },
  );
}
/** 更新不合格原因状态 */
export async function updataReasonStatus(data: { enabled: boolean; id: number }) {
  return await post(`${URLRequestPrefix.OM}/corp//unqualified-reasons/update-status`, data);
}
/** 更新不合格原因内容 */
export async function updataReasonContent(data: { value: string; id: number }) {
  return await post(`${URLRequestPrefix.OM}/corp//unqualified-reasons/update-value`, data);
}

/** 添加不合格原因内容 */
export async function addReasonContent(data: { data: string }) {
  return await post(`${URLRequestPrefix.OM}/corp//unqualified-reasons/add`, data);
}

/**
 * @description 获取AI自动识别列表
 * @returns
 */
export async function getAiAutoIdentifyList() {
  return await get(`${URLRequestPrefix.OM}/corp/private-ai/types`);
}

// 分页查询-稽核修改原因
export async function getAuditUnqualifiedReasonPage(data: any) {
  return await post(`${URLRequestPrefix.TM}/corp/audit/route/unqualified-reason/page`, data);
}

// 创建-稽核修改原因
export async function createAuditUnqualifiedReason(data: any) {
  return await post(`${URLRequestPrefix.TM}/corp/audit/route/unqualified-reason/create`, data);
}

// 启用-稽核修改原因
export async function enableAuditUnqualifiedReason(data: any) {
  return await post(`${URLRequestPrefix.TM}/corp/audit/route/unqualified-reason/enable`, data);
}

// 禁用-稽核修改原因
export async function disableAuditUnqualifiedReason(data: any) {
  return await post(`${URLRequestPrefix.TM}/corp/audit/route/unqualified-reason/disable`, data);
}

// 编辑-稽核修改原因
export async function editAuditUnqualifiedReason(data: any) {
  return await post(`${URLRequestPrefix.TM}/corp/audit/route/unqualified-reason/edit`, data);
}
